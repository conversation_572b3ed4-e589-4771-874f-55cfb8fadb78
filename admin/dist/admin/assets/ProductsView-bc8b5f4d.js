import{f as w,w as B,o as Q,U as o,V as i,$ as e,m as N,a7 as K,F as T,a6 as F,a1 as x,G as z,a2 as y,c as q,a5 as C,a0 as se,a8 as ie,_ as re,r as X,k as W,a4 as Y,u as M,a9 as Z,aa as de}from"./vendor-91c90871.js";import{a as ee}from"./api-a2dfd2a1.js";import{_ as ue}from"./utils-95cec1c7.js";import{l as ce}from"./categoryStore-7cbbd32e.js";import{_ as L}from"./index-928ecde2.js";import{a as te}from"./apiConfig-ad0108da.js";import"./ant-design-48c6fae4.js";const ge={class:"category-filter"},ve=["disabled"],me=["value"],fe={key:0,class:"text-xs text-gray-500 mt-1"},ye={key:0},pe={key:1},xe={key:1,class:"text-xs text-red-500 mt-1"},he={__name:"CategoryFilter",props:{modelValue:{type:[String,Number],default:""},showDebugInfo:{type:Boolean,default:!1}},emits:["update:modelValue","categoryChange"],setup(g,{emit:V}){const I=g,f=V,h=w(I.modelValue),r=w([]),c=w(!1),_=w(""),P=async()=>{try{c.value=!0,_.value="";const n=await ce();n.success?r.value=n.flatCategories:(_.value=n.message||"載入分類失敗",console.error("CategoryFilter載入分類失敗:",n))}catch(n){_.value="載入分類異常: "+n.message,console.error("CategoryFilter載入分類異常:",n)}finally{c.value=!1}},b=()=>{f("update:modelValue",h.value);const n={categoryId:h.value||""};f("categoryChange",n)};return B(()=>I.modelValue,n=>{h.value=n}),Q(()=>{P()}),(n,l)=>(o(),i("div",ge,[l[2]||(l[2]=e("label",{class:"block text-sm font-medium text-gray-700 mb-1"},"商品分類",-1)),N(e("select",{"onUpdate:modelValue":l[0]||(l[0]=v=>h.value=v),class:"filter-select w-full",onChange:b,disabled:c.value},[l[1]||(l[1]=e("option",{value:""},"全部分類",-1)),(o(!0),i(T,null,F(r.value,v=>(o(),i("option",{key:v.id,value:v.id},x(v.displayName),9,me))),128))],40,ve),[[K,h.value]]),g.showDebugInfo?(o(),i("div",fe,[z(" 共 "+x(r.value.length)+" 個分類可選 ",1),c.value?(o(),i("span",ye," (載入中...)")):y("",!0),h.value?(o(),i("span",pe," - 已選分類ID: "+x(h.value),1)):y("",!0)])):y("",!0),_.value?(o(),i("div",xe,x(_.value),1)):y("",!0)]))}},be=L(he,[["__scopeId","data-v-73dff886"]]);const _e={class:"admin-pagination"},ke={key:0,class:"pagination-info mb-4"},we={class:"text-sm text-gray-600"},Ce={class:"pagination-controls flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4"},Se={key:0,class:"flex items-center space-x-2"},$e=["disabled"],Pe=["value"],Ie={class:"pagination-buttons flex items-center space-x-1"},ze=["disabled"],Ve=["disabled"],Ae={key:0,class:"pagination-ellipsis"},Ne=["onClick","disabled"],Te={key:1,class:"pagination-ellipsis"},Fe=["disabled"],je=["disabled"],De={key:1,class:"flex items-center space-x-2"},Ue=["disabled","max"],We=["disabled"],Me={key:1,class:"pagination-loading-overlay"},Be={__name:"AdminPageItem",props:{total:{type:Number,required:!0},currentPage:{type:Number,default:1},pageSize:{type:Number,default:20},loading:{type:Boolean,default:!1},showSizeChanger:{type:Boolean,default:!0},showQuickJumper:{type:Boolean,default:!0},showTotal:{type:Boolean,default:!0}},emits:["pageChange","pageSizeChange"],setup(g,{expose:V,emit:I}){const f=g,h=I,r=w(f.currentPage),c=w(f.pageSize),_=w(""),P=[10,20,50,100],b=q(()=>Math.max(1,Math.ceil(f.total/c.value))),n=q(()=>{const d=b.value,u=r.value;return d<=7?Array.from({length:d},(p,S)=>S+1):u<=4?[1,2,3,4,5,6,7]:u>=d-3?Array.from({length:7},(p,S)=>d-6+S):Array.from({length:7},(p,S)=>u-3+S)}),l=q(()=>{const d=(r.value-1)*c.value+1,u=Math.min(r.value*c.value,f.total);return{start:d,end:u}}),v=d=>{d<1||d>b.value||d===r.value||f.loading||(r.value=d,h("pageChange",d))},E=()=>{r.value!==1&&!f.loading&&v(1)},R=()=>{r.value!==b.value&&!f.loading&&v(b.value)},j=()=>{r.value>1&&!f.loading&&v(r.value-1)},J=()=>{r.value<b.value&&!f.loading&&v(r.value+1)},D=d=>{f.loading||(c.value=d,r.value=1,h("pageSizeChange",d),h("pageChange",1))},U=()=>{const d=parseInt(_.value);d&&d>=1&&d<=b.value&&(v(d),_.value="")};return B(()=>f.total,d=>{const u=Math.max(1,Math.ceil(d/c.value));r.value>u&&(r.value=1,h("pageChange",1))}),B(()=>f.currentPage,d=>{d!==r.value&&(r.value=d)}),B(()=>f.pageSize,d=>{d!==c.value&&(c.value=d)}),Q(()=>{r.value=f.currentPage,c.value=f.pageSize}),V({nowPage:r,changePage:v,currentPageSize:c}),(d,u)=>(o(),i("div",_e,[g.showTotal?(o(),i("div",ke,[e("span",we," 顯示第 "+x(l.value.start)+" - "+x(l.value.end)+" 項，共 "+x(g.total)+" 項 ",1)])):y("",!0),e("div",Ce,[g.showSizeChanger?(o(),i("div",Se,[u[3]||(u[3]=e("span",{class:"text-sm text-gray-600"},"每頁顯示",-1)),N(e("select",{"onUpdate:modelValue":u[0]||(u[0]=p=>c.value=p),onChange:u[1]||(u[1]=p=>D(c.value)),disabled:g.loading,class:"form-select text-sm w-20"},[(o(),i(T,null,F(P,p=>e("option",{key:p,value:p},x(p),9,Pe)),64))],40,$e),[[K,c.value]]),u[4]||(u[4]=e("span",{class:"text-sm text-gray-600"},"項",-1))])):y("",!0),e("div",Ie,[e("button",{onClick:E,disabled:r.value===1||g.loading,class:C(["pagination-btn",{disabled:r.value===1||g.loading}]),title:"首頁"},u[5]||(u[5]=[e("i",{class:"fas fa-angle-double-left"},null,-1)]),10,ze),e("button",{onClick:j,disabled:r.value===1||g.loading,class:C(["pagination-btn",{disabled:r.value===1||g.loading}]),title:"上一頁"},u[6]||(u[6]=[e("i",{class:"fas fa-chevron-left"},null,-1)]),10,Ve),n.value[0]>1?(o(),i("span",Ae,"...")):y("",!0),(o(!0),i(T,null,F(n.value,p=>(o(),i("button",{key:p,onClick:S=>v(p),disabled:g.loading,class:C(["page-number-btn",{active:p===r.value,disabled:g.loading}])},x(p),11,Ne))),128)),n.value[n.value.length-1]<b.value?(o(),i("span",Te,"...")):y("",!0),e("button",{onClick:J,disabled:r.value===b.value||g.loading,class:C(["pagination-btn",{disabled:r.value===b.value||g.loading}]),title:"下一頁"},u[7]||(u[7]=[e("i",{class:"fas fa-chevron-right"},null,-1)]),10,Fe),e("button",{onClick:R,disabled:r.value===b.value||g.loading,class:C(["pagination-btn",{disabled:r.value===b.value||g.loading}]),title:"末頁"},u[8]||(u[8]=[e("i",{class:"fas fa-angle-double-right"},null,-1)]),10,je)]),g.showQuickJumper?(o(),i("div",De,[u[9]||(u[9]=e("span",{class:"text-sm text-gray-600"},"跳至",-1)),N(e("input",{"onUpdate:modelValue":u[2]||(u[2]=p=>_.value=p),onKeyup:ie(U,["enter"]),disabled:g.loading,type:"number",min:"1",max:b.value,placeholder:"頁碼",class:"form-input text-sm w-16"},null,40,Ue),[[se,_.value]]),u[10]||(u[10]=e("span",{class:"text-sm text-gray-600"},"頁",-1)),e("button",{onClick:U,disabled:g.loading,class:"btn-blue-sm"}," 跳轉 ",8,We)])):y("",!0)]),g.loading?(o(),i("div",Me,u[11]||(u[11]=[e("div",{class:"flex items-center justify-center"},[e("div",{class:"spinner mr-2"}),e("span",{class:"text-sm text-gray-600"},"載入中...")],-1)]))):y("",!0)]))}},qe=L(Be,[["__scopeId","data-v-0e6d137a"]]);const Ee={class:"products-admin"},Re={class:"mb-6 flex justify-end items-center"},Je={class:"bg-white p-4 rounded-lg shadow mb-6"},Qe={class:"grid grid-cols-1 md:grid-cols-4 gap-4 items-end"},Ke={class:"relative"},Le={class:"bg-white rounded-lg shadow overflow-hidden"},Oe={key:0,class:"px-6 py-3 bg-gray-50 border-b border-gray-200"},Ge={class:"flex items-center justify-between"},He={class:"flex items-center space-x-4 text-sm text-gray-600"},Xe={class:"flex items-center"},Ye={class:"ml-2"},Ze={key:0,class:"flex items-center text-green-600"},et={key:1,class:"flex items-center text-red-600"},tt={key:2,class:"flex items-center text-blue-600"},st={key:3,class:"flex items-center text-purple-600"},at={key:4,class:"flex items-center text-orange-600"},lt={key:0,class:"text-sm"},nt={key:1,class:"p-6 text-center"},ot={class:"space-y-4"},it={key:2,class:"p-6 text-center text-gray-500"},rt={key:3,class:"relative"},dt={class:"overflow-x-auto"},ut={class:"min-w-full divide-y divide-gray-200"},ct={class:"px-6 py-4 whitespace-nowrap"},gt={class:"w-16 h-16 bg-gray-200 rounded overflow-hidden relative flex items-center justify-center"},vt=["src"],mt={key:1,class:"text-xs text-gray-500"},ft={key:2,class:"absolute inset-0 bg-black bg-opacity-20 flex items-center justify-center"},yt={key:0,class:"flex items-center mt-1"},pt={key:1,class:"flex items-center mt-1"},xt={class:"px-6 py-4 whitespace-nowrap text-sm text-gray-500"},ht={class:"px-6 py-4 whitespace-nowrap text-sm text-gray-500"},bt={class:"px-6 py-4 whitespace-nowrap text-sm text-gray-500"},_t={key:0,class:"text-green-600"},kt={key:1},wt={class:"px-6 py-4 whitespace-nowrap text-sm text-gray-500"},Ct={class:"relative inline-block w-10 mr-2 align-middle select-none"},St=["id","checked","onChange"],$t=["for"],Pt={class:"px-6 py-4 whitespace-nowrap text-sm text-gray-500"},It={class:"relative inline-block w-10 mr-2 align-middle select-none"},zt=["id","checked","onChange"],Vt=["for"],At={class:"px-6 py-4 whitespace-nowrap text-sm text-gray-500"},Nt={class:"flex space-x-2"},Tt=["onClick"],Ft={key:0,class:"absolute inset-0 bg-white bg-opacity-75 flex items-center justify-center"},jt={key:0,class:"mt-6"},Dt={key:1,class:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50"},Ut={class:"bg-white rounded-lg shadow-lg w-full max-w-md p-6"},Wt={class:"mt-6 flex justify-end space-x-3"},Mt={__name:"ProductsView",setup(g){const{getAdminApiData:V,deleteAdminApiData:I,postAdminApiData:f}=ee();ee();const h=re(),r=w([]),c=w(!0),_=w(!1),P=w(null),b=q(()=>l.name!==""||l.categoryId!==""||l.status!=="active"),n=X({currentPage:1,pageSize:20,total:0}),l=X({name:"",categoryId:"",status:"active"}),v=async()=>{c.value=!0;try{const s={page:n.currentPage,pageSize:n.pageSize};if(l.name&&l.name.trim()!==""&&(s.keyWord=l.name.trim()),l.categoryId!==null&&l.categoryId!==void 0&&l.categoryId!==""){const m=String(l.categoryId).trim();m!==""&&(s.categoryId=m)}l.status&&l.status!==""&&(s.status=l.status);const t=await V("admin/products",s);if(t&&t.success){const m=t.products||[],a=t.total||0;r.value=m,n.total=a}else r.value=[],n.total=0}catch(s){console.error("載入商品異常:",s),r.value=[],n.total=0}finally{c.value=!1}},E=s=>{n.currentPage=s,d({page:s>1?s:null}),v(),j()},R=s=>{n.pageSize=s,n.currentPage=1,d({pageSize:s!==20?s:null,page:null}),v(),j()},j=()=>{try{window.scrollTo({top:0,behavior:"smooth"}),setTimeout(()=>{document.documentElement.scrollTop=0,document.body.scrollTop=0},100)}catch(s){console.log("滾動到頂部失敗:",s)}},J=s=>{const t=s.categoryId;l.categoryId=t!=null?String(t):"",n.currentPage=1,d({categoryId:l.categoryId||null,page:null}),v()},D=ue.debounce(()=>{n.currentPage=1,d({keyWord:l.name||null,page:null}),v()},800),U=()=>{l.name="",n.currentPage=1,d({keyWord:null,page:null}),v()},d=s=>{const t={...h.currentRoute.value.query};Object.entries(s).forEach(([m,a])=>{a==null||a===""?delete t[m]:t[m]=a}),h.replace({query:t})},u=()=>{const s=h.currentRoute.value.query;s.keyWord&&(l.name=s.keyWord),s.categoryId&&(l.categoryId=String(s.categoryId)),s.status&&(l.status=s.status),s.page&&(n.currentPage=parseInt(s.page)||1),s.pageSize&&(n.pageSize=parseInt(s.pageSize)||20)},p=s=>{P.value=s,_.value=!0},S=async()=>{try{const s=await I("admin/products",P.value.id);s&&s.success?(v(),_.value=!1,alert("商品已成功刪除")):alert(`刪除失敗: ${s?s.message:"未知錯誤"}`)}catch(s){console.error("刪除商品出錯:",s),alert("刪除商品時發生錯誤，請稍後再試")}},$=new Map,O=s=>{if(!s||!s.images)return null;const t=`${s.id}-${s.images}`;if($.has(t))return $.get(t);let m=null;if(Array.isArray(s.images))m=s.images;else if(typeof s.images=="string")try{m=JSON.parse(s.images)}catch{const k=s.images.trim();if(k.startsWith("http://")||k.startsWith("https://")){const A=k;return $.set(t,A),A}if(k.startsWith("/uploads/")){const A=te(k,"product");return $.set(t,A),A}return $.set(t,null),null}if(m&&Array.isArray(m)&&m.length>0){let a=m[0];if(a.startsWith("http://")||a.startsWith("https://"))return $.set(t,a),a;const k=te(a,"product");return $.set(t,k),k}return $.set(t,null),null},ae=s=>{const t=s.target.src;s.target.style.display="none";const m=s.target.parentNode;if(m&&!m.querySelector(".error-text")){const a=document.createElement("span");a.textContent=t.includes("placeholder.com")?"圖片無法載入":"載入錯誤",a.className="text-xs text-red-500 error-text",m.appendChild(a)}},G=s=>s==null?"0":s.toString().replace(/\B(?=(\d{3})+(?!\d))/g,","),le=async s=>{try{const t=await f("admin/products/toggle-featured",{id:s.id,is_featured:!s.is_featured});t&&t.success?s.is_featured=!s.is_featured:alert("更新特色商品狀態失敗："+((t==null?void 0:t.message)||"未知錯誤"))}catch(t){console.error("更新特色商品狀態時發生錯誤:",t),alert("更新特色商品狀態時發生錯誤，請稍後再試")}},ne=async s=>{try{const t=await f("admin/products/toggle-active",{id:s.id,is_active:!s.is_active});if(t&&t.success){s.is_active=!s.is_active;const m=s.is_active?"啟用":"停用";alert(`商品已${m}`)}else alert("更新商品狀態失敗："+((t==null?void 0:t.message)||"未知錯誤"))}catch(t){console.error("更新商品狀態時發生錯誤:",t),alert("更新商品狀態時發生錯誤，請稍後再試")}};Q(async()=>{u(),v()});const oe=()=>{n.currentPage=1,d({page:null,status:l.status==="active"?null:l.status}),v()},H=()=>{l.name="",l.categoryId="",l.status="active",n.currentPage=1,d({keyWord:null,categoryId:null,status:null,page:null,pageSize:n.pageSize!==20?n.pageSize:null}),v()};return(s,t)=>{var m;return o(),i("div",Ee,[e("div",Re,[W(M(Z),{to:"products/add",class:"btn-primary"},{default:Y(()=>t[5]||(t[5]=[e("i",{class:"fas fa-plus mr-2"},null,-1),z(" 新增商品 ")])),_:1,__:[5]})]),e("div",Je,[e("div",Qe,[e("div",null,[t[7]||(t[7]=e("label",{class:"block text-sm font-medium text-gray-700 mb-1"},"商品名稱",-1)),e("div",Ke,[N(e("input",{type:"text","onUpdate:modelValue":t[0]||(t[0]=a=>l.name=a),placeholder:"搜尋商品名稱",class:"filter-input w-full",onInput:t[1]||(t[1]=(...a)=>M(D)&&M(D)(...a))},null,544),[[se,l.name]]),l.name?(o(),i("button",{key:0,onClick:U,class:"absolute right-2 top-1/2 -translate-y-1/2 text-gray-400 hover:text-gray-600"},t[6]||(t[6]=[e("i",{class:"fas fa-times"},null,-1)]))):y("",!0)])]),e("div",null,[W(be,{modelValue:l.categoryId,"onUpdate:modelValue":t[2]||(t[2]=a=>l.categoryId=a),onCategoryChange:J},null,8,["modelValue"])]),e("div",null,[t[9]||(t[9]=e("label",{class:"block text-sm font-medium text-gray-700 mb-1"},"商品狀態",-1)),N(e("select",{"onUpdate:modelValue":t[3]||(t[3]=a=>l.status=a),onChange:oe,class:"filter-select w-full"},t[8]||(t[8]=[e("option",{value:"active"},"僅顯示啟用商品",-1),e("option",{value:"inactive"},"僅顯示停用商品",-1),e("option",{value:"all"},"顯示全部商品",-1)]),544),[[K,l.status]])]),e("div",null,[t[11]||(t[11]=e("label",{class:"block text-sm font-medium text-gray-700 mb-1"}," ",-1)),e("button",{onClick:H,class:"filter-button w-full",title:"重置所有篩選條件"},t[10]||(t[10]=[e("i",{class:"fas fa-undo mr-2"},null,-1),z(" 重置篩選 ")]))])])]),e("div",Le,[!c.value||r.value.length>0?(o(),i("div",Oe,[e("div",Ge,[e("div",He,[e("div",Xe,[t[12]||(t[12]=e("span",{class:"font-medium"},"篩選結果:",-1)),e("span",Ye,x(n.total)+" 個商品",1)]),l.status==="active"?(o(),i("div",Ze,t[13]||(t[13]=[e("i",{class:"fas fa-check-circle mr-1"},null,-1),e("span",null,"僅顯示啟用商品",-1)]))):l.status==="inactive"?(o(),i("div",et,t[14]||(t[14]=[e("i",{class:"fas fa-ban mr-1"},null,-1),e("span",null,"僅顯示停用商品",-1)]))):(o(),i("div",tt,t[15]||(t[15]=[e("i",{class:"fas fa-list mr-1"},null,-1),e("span",null,"顯示全部商品",-1)]))),l.name?(o(),i("div",st,[t[16]||(t[16]=e("i",{class:"fas fa-search mr-1"},null,-1)),e("span",null,'搜尋: "'+x(l.name)+'"',1)])):y("",!0),l.categoryId?(o(),i("div",at,t[17]||(t[17]=[e("i",{class:"fas fa-tags mr-1"},null,-1),e("span",null,"分類篩選中",-1)]))):y("",!0)]),b.value?(o(),i("div",lt,[e("button",{onClick:H,class:"text-blue-600 hover:text-blue-800 underline"},t[18]||(t[18]=[e("i",{class:"fas fa-undo mr-1"},null,-1),z(" 清除篩選 ")]))])):y("",!0)])])):y("",!0),c.value&&r.value.length===0?(o(),i("div",nt,[t[20]||(t[20]=e("div",{class:"flex items-center justify-center mb-4"},[e("div",{class:"spinner mr-3"}),e("span",{class:"text-gray-600"},"載入商品資料中...")],-1)),e("div",ot,[(o(),i(T,null,F(5,a=>e("div",{key:a,class:"animate-pulse"},t[19]||(t[19]=[de('<div class="flex items-center space-x-4 p-4 border-b" data-v-1c3760c0><div class="w-16 h-16 bg-gray-200 rounded" data-v-1c3760c0></div><div class="flex-1 space-y-2" data-v-1c3760c0><div class="h-4 bg-gray-200 rounded w-3/4" data-v-1c3760c0></div><div class="h-3 bg-gray-200 rounded w-1/2" data-v-1c3760c0></div></div><div class="w-24 h-4 bg-gray-200 rounded" data-v-1c3760c0></div><div class="w-20 h-4 bg-gray-200 rounded" data-v-1c3760c0></div></div>',1)]))),64))])])):!c.value&&r.value.length===0?(o(),i("div",it,t[21]||(t[21]=[e("i",{class:"fas fa-box-open text-5xl mb-4"},null,-1),e("p",null,"暫無商品資料",-1)]))):(o(),i("div",rt,[e("div",dt,[e("table",ut,[t[27]||(t[27]=e("thead",{class:"bg-gray-50"},[e("tr",null,[e("th",{class:"th"},"商品圖片"),e("th",{class:"th"},"商品名稱"),e("th",{class:"th"},"分類"),e("th",{class:"th"},"價格"),e("th",{class:"th"},"特價"),e("th",{class:"th"},"特色商品"),e("th",{class:"th"},"商品狀態"),e("th",{class:"th"},"操作")])],-1)),e("tbody",{class:C(["bg-white divide-y divide-gray-200",{"opacity-60":c.value}])},[(o(!0),i(T,null,F(r.value,a=>(o(),i("tr",{key:a.id,class:C(["hover:bg-gray-50 transition-colors",{"opacity-50":!a.is_active&&l.status==="all","bg-red-50":!a.is_active&&l.status==="inactive"}])},[e("td",ct,[e("div",gt,[O(a)?(o(),i("img",{key:0,src:O(a),alt:"商品圖片",class:"w-full h-full object-cover",onError:ae,loading:"lazy"},null,40,vt)):(o(),i("span",mt,"無圖片")),a.is_active?y("",!0):(o(),i("div",ft,t[22]||(t[22]=[e("i",{class:"fas fa-ban text-red-500 text-xl"},null,-1)])))])]),e("td",{class:C(["px-6 py-4 whitespace-nowrap text-sm font-medium",a.is_active?"text-gray-900":"text-gray-500"])},[z(x(a.name)+" ",1),a.is_active?a.is_featured?(o(),i("div",pt,t[24]||(t[24]=[e("span",{class:"px-2 py-1 text-xs bg-blue-100 text-blue-800 rounded"},"特色商品",-1)]))):y("",!0):(o(),i("div",yt,t[23]||(t[23]=[e("span",{class:"px-2 py-1 text-xs bg-red-100 text-red-800 rounded"},"已停用",-1)])))],2),e("td",xt,x(a.category_path||a.sort||"未分類"),1),e("td",ht," NT$ "+x(G(a.price1)),1),e("td",bt,[a.price2?(o(),i("span",_t,"NT$ "+x(G(a.price2)),1)):(o(),i("span",kt,"-"))]),e("td",wt,[e("div",Ct,[e("input",{type:"checkbox",id:"toggle-featured-"+a.id,checked:a.is_featured,onChange:k=>le(a),class:"toggle-checkbox absolute block w-6 h-6 rounded-full bg-white border-4 appearance-none cursor-pointer"},null,40,St),e("label",{for:"toggle-featured-"+a.id,class:"toggle-label block overflow-hidden h-6 rounded-full bg-gray-300 cursor-pointer"},null,8,$t)])]),e("td",Pt,[e("div",It,[e("input",{type:"checkbox",id:"toggle-active-"+a.id,checked:a.is_active,onChange:k=>ne(a),class:"toggle-checkbox-active absolute block w-6 h-6 rounded-full bg-white border-4 appearance-none cursor-pointer"},null,40,zt),e("label",{for:"toggle-active-"+a.id,class:"toggle-label-active block overflow-hidden h-6 rounded-full bg-gray-300 cursor-pointer"},null,8,Vt)]),e("span",{class:C(["text-xs ml-2",a.is_active?"text-green-600":"text-red-600"])},x(a.is_active?"啟用":"停用"),3)]),e("td",At,[e("div",Nt,[W(M(Z),{to:{path:`products/edit/${a.id}`,query:{keyword:l.name||void 0,categoryId:l.categoryId||void 0,page:n.currentPage>1?n.currentPage:void 0,pageSize:n.pageSize!==20?n.pageSize:void 0}},class:"text-blue-600 hover:text-blue-900 p-1 rounded hover:bg-blue-100",title:"編輯"},{default:Y(()=>t[25]||(t[25]=[e("i",{class:"fas fa-edit"},null,-1)])),_:2,__:[25]},1032,["to"]),e("button",{onClick:k=>p(a),class:"text-red-600 hover:text-red-900 p-1 rounded hover:bg-red-100",title:"刪除"},t[26]||(t[26]=[e("i",{class:"fas fa-trash"},null,-1)]),8,Tt)])])],2))),128))],2)])]),c.value?(o(),i("div",Ft,t[28]||(t[28]=[e("div",{class:"flex items-center"},[e("div",{class:"spinner mr-3"}),e("span",{class:"text-gray-600"},"更新資料中...")],-1)]))):y("",!0)]))]),!c.value||r.value.length>0?(o(),i("div",jt,[W(qe,{total:n.total,currentPage:n.currentPage,pageSize:n.pageSize,loading:c.value,onPageChange:E,onPageSizeChange:R},null,8,["total","currentPage","pageSize","loading"])])):y("",!0),_.value?(o(),i("div",Dt,[e("div",Ut,[t[30]||(t[30]=e("h3",{class:"text-xl font-semibold mb-4"},"確認刪除",-1)),e("p",null,"您確定要刪除商品「"+x((m=P.value)==null?void 0:m.name)+"」嗎？此操作無法還原。",1),e("div",Wt,[e("button",{onClick:t[4]||(t[4]=a=>_.value=!1),class:"btn-gray"},"取消"),e("button",{onClick:S,class:"btn-red"},t[29]||(t[29]=[e("i",{class:"fas fa-trash mr-2"},null,-1),z(" 確認刪除 ")]))])])])):y("",!0)])}}},Lt=L(Mt,[["__scopeId","data-v-1c3760c0"]]);export{Lt as default};
