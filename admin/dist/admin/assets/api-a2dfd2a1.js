import{a as u}from"./utils-95cec1c7.js";import{r as w}from"./index-928ecde2.js";import{A as F,b as L,a as N,g}from"./apiConfig-ad0108da.js";import{m as I}from"./ant-design-48c6fae4.js";const $=u.create({baseURL:F,timeout:L.timeout,withCredentials:L.withCredentials});$.interceptors.request.use(async a=>{const i=localStorage.getItem("adminToken"),l=localStorage.getItem("token"),d=i||l;return d&&(a.headers.Authorization=`Bearer ${d}`),a},a=>Promise.reject(a));$.interceptors.response.use(a=>a,async a=>{var i,l,d;if(a.response){const{status:T,data:m}=a.response;if(T===401)return console.warn("🔐 後台API收到401未授權錯誤 - Token可能已過期"),S("後台API"),Promise.reject(a);if(T===403)return console.warn("🚫 後台API收到403禁止訪問錯誤 - 權限不足"),(i=m==null?void 0:m.message)!=null&&i.includes("token")||(l=m==null?void 0:m.message)!=null&&l.includes("過期")||(d=m==null?void 0:m.message)!=null&&d.includes("管理員")?S("後台API"):I.error("權限不足，無法執行此操作"),Promise.reject(a);if(T>=500)return console.error("🔥 後台伺服器錯誤:",T,m==null?void 0:m.message),I.error("伺服器錯誤，請稍後再試"),Promise.reject(a)}return!a.response&&a.code==="ECONNABORTED"&&(console.error("⏱️ 後台請求超時"),I.error("請求超時，請檢查網路連線")),Promise.reject(a)});const S=(a="後台")=>{console.warn(`🔐 ${a} Token已過期或無效，執行管理員強制登出`),I.warning("管理員登入已過期，請重新登入",3),W(),setTimeout(()=>{_()},1e3)},W=()=>{try{["adminToken","adminUser","adminData","adminSession"].forEach(d=>{localStorage.removeItem(d)}),["token","isLogin","user","userData","belongCp"].forEach(d=>{localStorage.removeItem(d)}),["adminSession","userSession","authSession"].forEach(d=>{sessionStorage.removeItem(d)}),console.log("✅ 已清除所有管理員認證資料")}catch(a){console.error("❌ 清除管理員認證資料時發生錯誤:",a)}},_=()=>{var a,i;try{window.adminStore&&((i=(a=window.adminStore).$reset)==null||i.call(a));const l=window.location.pathname;l!=="/login"&&l!=="/"&&(console.log("🔄 重導向到管理員登入頁面"),w.push("/login").catch(d=>{console.warn("管理員路由跳轉警告:",d),window.location.href="/login"}))}catch(l){console.error("❌ 管理員強制登出時發生錯誤:",l),window.location.href="/login"}},C=(a="")=>{console.warn(`⚠️ 後台API ${a} 收到401錯誤，使用統一的token過期處理`),S(`後台API-${a}`)},y=()=>({"Content-Type":"application/json",Authorization:`Bearer ${localStorage.getItem("token")}`}),G=()=>{const a=localStorage.getItem("adminToken");return{"Content-Type":"application/json",Authorization:a?`Bearer ${a}`:void 0}},P=()=>({userId:localStorage.getItem("user"),belongCp:localStorage.getItem("belongCp")});function Y(){let a=null,i=null,l=null;const d=u.CancelToken.source(),T=async function(e,s){try{let t=e;e==="product"?t="products":e==="basic"?t="basic/info":e==="user"?t="users/profile":e==="orderList"?t="orderList":e==="comments"?t="basic/comments":e==="user/comments"&&(t="basic/user/comments");const o=await u.get(g(t),{params:{...s||{},sort:(s==null?void 0:s.sort)==="特色商品"?"特色商品":s==null?void 0:s.sort},headers:y(),cancelToken:d.token});return!o||!o.data?(console.error("API響應無效:",o),{resData:[]}):e==="user"?(a=o.data.body,{resData:a,resTotal:i,resTotalMoney:l,success:o.data.success}):e==="orderList"?(a=o.data.resData||[],i=o.data.total||0,{resData:a,resTotal:i,success:o.data.success}):e==="product"?(o.data.body?a=o.data.body:o.data.resData?a=o.data.resData:a=[],i=o.data.total||0,l=o.data.totalMoney||0,{resData:a,resTotal:i,resTotalMoney:l}):e==="brands"?{resData:o.data.resData||[],success:o.data.success,resTotal:o.data.total,resTotalMoney:o.data.totalMoney}:e==="comments"||e==="user/comments"?{resData:o.data.resData||[],error:o.data.error,status:o.data.status}:e==="basic/carousel"?{success:o.data.success,data:o.data.data||[],error:o.data.error}:(l=o.data.totalMoney,i=o.data.total,a=o.data.body,{resData:a,resTotal:i,resTotalMoney:l})}catch(t){return console.error("API 請求錯誤:",t),t.response&&t.response.status===401&&C(e),{resData:[],error:t.message}}},m=async function(e,s){try{const t={...P()},o={...y()};e=="product"?(t.shop=s.shop??localStorage.shop,t.belongCp=93468487,t.userId="chengshe"):e=="user"?(t.userId=s.userId,t.id=s.id,o.Authorization=`Bearer ${localStorage.getItem("token")}`):e=="orderList"&&s.status==3?t.isMail=1:e=="basic"&&(t.ID=P().belongCp);let n=`${e}/update`;return e==="user"?n=`users/update/${s.id}`:e.startsWith("comments/")&&(n=`basic/${e}`),await u.patch(g(n),s,{params:{...t},headers:o})}catch(t){return console.error("Error fetching client data:",t),{status:500,error:t.message}}},E=async function(e,s){var t,o;try{const n={...P()},r={...y()};return(await u.put(g(e),s,{params:{...n,id:s.id},headers:r})).data}catch(n){return console.error("更新資料時發生錯誤:",n),{success:!1,message:((o=(t=n.response)==null?void 0:t.data)==null?void 0:o.message)||"更新失敗，請稍後再試"}}},M=async function(e,s){var t,o;try{let n=e;e==="product"?n="products":e==="user"?n="users/register":e==="comments"&&(n="basic/comments");const r=await u.post(g(n),s,{headers:y()});return r.data.success===!1?(I.error(r.data.message||"新增失敗"),null):r.data}catch(n){return console.error("Error in postApiData:",n),n.response&&n.response.status===401?(C(),null):(I.error(((o=(t=n.response)==null?void 0:t.data)==null?void 0:o.message)||"新增失敗"),null)}},j=async function(e,s){var t,o;try{const n={...P()};let r=e;e==="product"?(n.shop=localStorage.shop,n.belongCp=93468487,n.userId="chengshe",r=`${e}/del`):e.startsWith("comments/")?r=`basic/${e}`:r=`${e}/del`;let c=s;typeof s=="object"&&s.id&&(c=s.id);let f;return e.startsWith("comments/")?f=await u.delete(g(r),{headers:y()}):f=await u.delete(g(r),{params:{...n,id:c,isUserDel:0},headers:y()}),console.log("刪除結果:",f.data),f.data||{success:!0}}catch(n){return console.error("Error fetching client data:",n),{success:!1,message:((o=(t=n.response)==null?void 0:t.data)==null?void 0:o.message)||"刪除失敗，請稍後再試"}}},U=async function(e){try{const s={...P()};await u.patch(g("orderList/updateRead"),e,{params:{...s,id:e.id,isRead:1},headers:y()})}catch(s){console.error("Error fetching client data:",s)}},H=async function(e){try{return(await u.post(g("user/login"),{mail:e.mail,password:e.password})).data.body.token}catch(s){console.error("Error fetching client data:",s)}},B=()=>{d.cancel("Operation canceled by the user.")},D=async function(e){try{console.log("checkHl",e);const s=await u.get(g("hlList"),{params:{...P(),user:e.id},headers:y()});console.log("checkHl",s.data.body);const t=s.data.body.find(o=>o.isDelete==0);console.log(t)}catch(s){console.error("Error fetching client data:",s)}},z=async function(e,s){var t,o,n,r;try{const c=localStorage.getItem("adminLogin"),f=localStorage.getItem("adminToken");if(c!=="true")return console.error("❌ 管理員API請求錯誤: 未登入或無效令牌"),{success:!1,message:"您的管理員登入已過期，請重新登入"};const h=g(e),A={"Content-Type":"application/json",Authorization:`Bearer ${f}`},p=await u.get(h,{params:s,headers:A});return!p||!p.data?(console.error("❌ API響應無效:",p),{success:!1,message:"伺服器返回無效響應",data:[]}):p.data}catch(c){return console.error("❌ Admin API 請求錯誤:",c.message),((t=c.response)==null?void 0:t.status)===401?(I.error("管理員登入已過期，請重新登入"),w.push("/login"),{success:!1,message:"管理員登入已過期，請重新登入",errorCode:401,data:[]}):{success:!1,message:((n=(o=c.response)==null?void 0:o.data)==null?void 0:n.message)||"獲取數據失敗，請檢查您的管理員權限",errorCode:((r=c.response)==null?void 0:r.status)||500,data:[]}}},R=async function(e,s){var t,o,n;try{const r=localStorage.getItem("adminLogin"),c=localStorage.getItem("adminToken");if(r==="true"&&!c)return console.log("本機模式 - 模擬更新數據:",e,s),{success:!0,message:"數據更新成功 (模擬模式)",data:s};const h=s.id,A=h?{id:h}:{},p=g(e);console.log("發送PUT請求:",p,s);const k=await u.put(p,s,{params:A,headers:{"Content-Type":"application/json",Authorization:`Bearer ${c}`}});return console.log("PUT請求成功:",k.data),k.data}catch(r){return console.error("Admin API 更新錯誤:",r.message),console.error("錯誤詳情:",((t=r.response)==null?void 0:t.data)||r),{success:!1,message:((n=(o=r.response)==null?void 0:o.data)==null?void 0:n.message)||"更新失敗，請稍後再試"}}},v=async function(e,s){var t,o,n;try{const r=localStorage.getItem("adminLogin"),c=localStorage.getItem("adminToken");if(r==="true"&&!c)return console.log("本機模式 - 模擬部分更新數據:",e,s),{success:!0,message:"數據部分更新成功 (模擬模式)",data:s};const h=g(e);console.log("發送PATCH請求:",h,s);const A=await u.patch(h,s,{headers:{"Content-Type":"application/json",Authorization:`Bearer ${c}`}});return console.log("PATCH請求成功:",A.data),A.data}catch(r){return console.error("Admin API PATCH 錯誤:",r.message),console.error("錯誤詳情:",((t=r.response)==null?void 0:t.data)||r),{success:!1,message:((n=(o=r.response)==null?void 0:o.data)==null?void 0:n.message)||"更新失敗，請稍後再試"}}},K=async function(e,s){var t,o,n;try{const r=localStorage.getItem("adminLogin"),c=localStorage.getItem("adminToken");if(r==="true"&&!c)return console.log("本機模式 - 模擬新增數據:",e,s),e==="admin/orders/batch-delete"&&s.ids?{success:!0,message:`已成功刪除 ${s.ids.length} 筆訂單 (模擬模式)`}:{success:!0,message:"數據新增成功 (模擬模式)",data:{...s,id:Math.floor(Math.random()*1e3)+1e3}};const h=s instanceof FormData;let A={Authorization:`Bearer ${c}`};h||(A["Content-Type"]="application/json");const p=g(e);console.log("正在發送POST請求到:",p);const k=await u.post(p,s,{headers:A});return console.log("POST請求成功:",k.data),k.data}catch(r){return console.error("Admin API 新增錯誤:",r.message),console.error("錯誤詳情:",((t=r.response)==null?void 0:t.data)||r),{success:!1,message:((n=(o=r.response)==null?void 0:o.data)==null?void 0:n.message)||r.message||"新增失敗"}}},O=async function(e,s={}){var t,o,n;try{let r=s;(typeof s=="string"||typeof s=="number")&&(r={id:s});const c=await u.delete(g(e),{data:r,headers:G()});return console.log("刪除管理員API結果:",c.data),c.data||{success:!0}}catch(r){return console.error("刪除管理員API數據時發生錯誤:",r),b(e,r),((t=r.response)==null?void 0:t.status)===401?(x(),{success:!1,message:"身份驗證失敗，請重新登入"}):{success:!1,message:((n=(o=r.response)==null?void 0:o.data)==null?void 0:n.message)||"刪除失敗，請稍後再試"}}},q=()=>{const e=localStorage.getItem("token");return!e||e==="undefined"||e==="null"?(console.error("無效的認證令牌:",e),!1):!0},b=(e,s)=>{console.error(`API 錯誤 [${e}]:`,s),s.response?(console.error("響應狀態:",s.response.status),console.error("響應數據:",s.response.data)):s.request?console.error("請求發送但無響應"):console.error("錯誤信息:",s.message)},x=()=>{localStorage.removeItem("adminLogin"),localStorage.removeItem("adminToken"),localStorage.removeItem("adminInfo"),I.error("管理員身份驗證失敗，請重新登入"),window.location.pathname!=="/login"&&w.push("/login")};return{getApiData:T,patchApiData:m,postApiData:M,delApiData:j,getUserToken:H,cancelRequests:B,updateRead:U,checkHl:D,updateApiData:E,getAdminApiData:z,updateAdminApiData:R,patchAdminApiData:v,postAdminApiData:K,deleteAdminApiData:O,getImageUrl:N,checkAuthToken:q,logApiError:b}}export{Y as a};
