import{f as n,r as B,o as Z,U as o,V as i,$ as e,a5 as D,a2 as y,G as _,F as ee,a6 as te,a1 as g,k as se,u as le,aa as ae,m as w,a0 as A,ae as T}from"./vendor-91c90871.js";import{a as oe}from"./api-a2dfd2a1.js";import{f as ie}from"./style-5fb4a138.js";import{A as re,a as ne}from"./apiConfig-ad0108da.js";import{_ as de}from"./index-928ecde2.js";import"./utils-95cec1c7.js";import"./ant-design-48c6fae4.js";const ue={class:"settings-admin"},ce={class:"bg-white rounded-lg shadow mb-6"},pe={class:"flex border-b"},me={key:0,class:"bg-white rounded-lg shadow p-6"},ve={key:1,class:"bg-white rounded-lg shadow p-6"},fe={class:"mb-6 flex justify-between items-center"},ge={key:0,class:"p-6 text-center"},xe={key:1,class:"p-6 text-center text-gray-500"},be={key:2,class:"overflow-x-auto"},ye={class:"min-w-full divide-y divide-gray-200"},_e={class:"bg-white divide-y divide-gray-200"},we={class:"px-6 py-4 whitespace-nowrap"},he={class:"flex-shrink-0 h-20 w-32"},ke=["src"],Ce={class:"px-6 py-4 whitespace-nowrap"},Ae={class:"text-sm font-medium text-gray-900"},Ue={key:0,class:"text-sm text-gray-500 truncate max-w-xs"},je={class:"px-6 py-4 whitespace-nowrap"},Ve={key:0,class:"text-sm text-blue-500 truncate max-w-xs"},De=["href"],$e={key:1,class:"text-sm text-gray-500"},Ie={class:"px-6 py-4 whitespace-nowrap"},Pe={class:"px-6 py-4 whitespace-nowrap text-sm text-gray-500"},Re={class:"px-6 py-4 whitespace-nowrap text-right text-sm font-medium"},Ee=["onClick"],Fe=["onClick"],Be={key:2,class:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50"},Te={class:"bg-white rounded-lg shadow-lg w-full max-w-lg p-6"},Le={class:"text-xl font-semibold mb-4"},Me={class:"space-y-4"},Se={class:"form-group"},Ne={class:"mt-1"},ze={key:0,class:"mb-4"},Ge={class:"mb-2"},Oe={class:"border rounded-md p-2",style:{height:"600px","max-width":"800px"}},We={key:1,class:"flex items-center"},Je={key:0,class:"relative flex-shrink-0 h-40 w-full max-w-md border rounded-md overflow-hidden"},He=["src"],qe={key:1,class:"flex-shrink-0 h-40 w-full max-w-md border-2 border-dashed border-gray-300 rounded-md flex items-center justify-center"},Ke={for:"fileUpload",class:"cursor-pointer text-center p-4"},Qe={key:0,class:"text-red-500"},Xe={class:"form-group"},Ye={class:"form-group"},Ze={class:"form-group"},et={class:"grid grid-cols-1 md:grid-cols-2 gap-4"},tt={class:"form-group"},st={class:"form-group"},lt={class:"mt-2"},at={class:"inline-flex items-center mr-4"},ot={class:"inline-flex items-center"},it={class:"mt-6 flex justify-end space-x-3"},rt=["disabled"],nt={key:0},dt={key:1},ut={key:3,class:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50"},ct={class:"bg-white rounded-lg shadow-lg w-full max-w-md p-6"},pt={class:"mt-6 flex justify-end space-x-3"},mt={__name:"SettingsView",setup(vt){const{getAdminApiData:L,postAdminApiData:M,updateAdminApiData:S,deleteAdminApiData:N}=oe(),x=n("carousel"),d=n([]),U=n(!0),k=n(!1),j=n(!1),b=n(!1),C=n(!1),l=B({id:null,title:"",description:"",link:"",image_url:"",is_active:1,sort_order:0}),m=B({image:""}),u=n(""),h=n(null),v=n(null),c=n(!1),f=n(""),p=n(null),z=n({aspectRatio:null,minWidth:200,minHeight:100,movable:!0,resizable:!0}),$=n(null);Z(()=>{V()});const V=async()=>{U.value=!0;try{console.log("開始載入輪播圖...");const s=await L("admin/carousel");console.log("輪播圖API回應:",s),s&&s.success?(Array.isArray(s.data)?d.value=s.data:s.data&&Array.isArray(s.data.data)?d.value=s.data.data:d.value=[],console.log("載入輪播圖成功:",d.value.length,"個輪播圖")):(console.error("載入輪播圖失敗:",s?s.message||s.error:"未知錯誤"),d.value=[])}catch(s){console.error("載入輪播圖時出錯:",s),d.value=[]}finally{U.value=!1}},I=s=>s?ne(s,"banner"):"/no-image.jpg",G=s=>{const t=s.target.files[0];if(!t)return;if(!["image/jpeg","image/png","image/webp","image/gif"].includes(t.type)){m.image="只支援JPG、PNG、WEBP和GIF格式的圖片";return}if(t.size>5*1024*1024){m.image="圖片大小不能超過5MB";return}m.image="";const r=new FileReader;r.onload=E=>{f.value=E.target.result,c.value=!0},r.readAsDataURL(t)},O=s=>{p.value=s},W=()=>{if(p.value&&p.value.canvas){const s=p.value.canvas,{width:t,height:a}=p.value.coordinates,r=document.createElement("canvas");r.width=t,r.height=a,r.getContext("2d").drawImage(s,0,0,r.width,r.height),r.toBlob(F=>{const Y=new File([F],"cropped-image.jpg",{type:"image/jpeg"});h.value=Y,u.value=URL.createObjectURL(F),c.value=!1},"image/jpeg",.9)}},J=()=>{c.value=!1,f.value="",v.value&&(v.value.value="")},H=async()=>{if(u.value)try{const t=await(await fetch(u.value)).blob(),a=new FileReader;a.onload=r=>{f.value=r.target.result,c.value=!0},a.readAsDataURL(t)}catch(s){console.error("載入圖片進行編輯時出錯:",s),alert("無法載入圖片進行編輯，請稍後再試")}},P=()=>{u.value="",h.value=null,f.value="",c.value=!1,p.value=null,v.value&&(v.value.value="")},q=s=>{c.value=!1,f.value="",p.value=null,l.id=s.id,l.title=s.title||"",l.description=s.description||"",l.link=s.link||"",l.image_url=s.image_url,l.is_active=s.is_active,l.sort_order=s.sort_order||0,u.value=I(s.image_url),v.value&&(v.value.value=""),h.value=null,b.value=!0},K=s=>{$.value=s,C.value=!0},Q=async()=>{try{const s=await N(`admin/carousel/${$.value.id}`);s.success?(V(),C.value=!1,alert("輪播圖已成功刪除")):alert(`刪除失敗: ${s.message||"未知錯誤"}`)}catch(s){console.error("刪除輪播圖出錯:",s),alert("刪除輪播圖時發生錯誤，請稍後再試")}},R=()=>{j.value=!1,b.value=!1,c.value=!1,l.id=null,l.title="",l.description="",l.link="",l.image_url="",l.is_active=1,l.sort_order=0,P(),f.value="",p.value=null,m.image=""},X=async()=>{if(!u.value&&!l.image_url){m.image="請上傳輪播圖片";return}k.value=!0;try{let s;if(h.value){const t=new FormData;t.append("image",h.value);const a=await fetch(`${re}/admin/upload/single?type=carousel&subdir=banner`,{method:"POST",headers:{Authorization:`Bearer ${localStorage.getItem("adminToken")}`},body:t}).then(r=>r.json());if(!a.success)throw new Error(a.message||"圖片上傳失敗");l.image_url=a.imageUrl}if(b.value?s=await S(`admin/carousel/${l.id}`,{title:l.title.trim()||null,description:l.description.trim()||null,link:l.link.trim()||null,image_url:l.image_url,is_active:l.is_active,sort_order:l.sort_order||0}):s=await M("admin/carousel",{title:l.title.trim()||null,description:l.description.trim()||null,link:l.link.trim()||null,image_url:l.image_url,is_active:l.is_active,sort_order:l.sort_order||0}),s.success)await V(),R(),alert(b.value?"輪播圖更新成功！":"輪播圖新增成功！");else throw new Error(s.message||"操作失敗")}catch(s){console.error("保存輪播圖失敗:",s),alert(`保存輪播圖失敗: ${s.message||"未知錯誤"}`)}finally{k.value=!1}};return(s,t)=>(o(),i("div",ue,[e("div",ce,[e("div",pe,[e("button",{onClick:t[0]||(t[0]=a=>x.value="basic"),class:D(["py-3 px-6 font-medium text-sm",x.value==="basic"?"border-b-2 border-blue-500 text-blue-600":"text-gray-500 hover:text-gray-700"])}," 基本設定 ",2),e("button",{onClick:t[1]||(t[1]=a=>x.value="carousel"),class:D(["py-3 px-6 font-medium text-sm",x.value==="carousel"?"border-b-2 border-blue-500 text-blue-600":"text-gray-500 hover:text-gray-700"])}," 首頁輪播圖 ",2)])]),x.value==="basic"?(o(),i("div",me,t[10]||(t[10]=[e("p",{class:"text-gray-500"},"基本設定功能將在未來添加...",-1)]))):y("",!0),x.value==="carousel"?(o(),i("div",ve,[e("div",fe,[t[12]||(t[12]=e("h3",{class:"text-lg font-medium"},"輪播圖管理",-1)),e("button",{onClick:t[2]||(t[2]=a=>j.value=!0),class:"btn-primary"},t[11]||(t[11]=[e("i",{class:"fas fa-plus mr-2"},null,-1),_(" 新增輪播圖 ")]))]),U.value?(o(),i("div",ge,t[13]||(t[13]=[e("div",{class:"spinner"},null,-1),e("p",{class:"mt-2 text-gray-600"},"載入中...",-1)]))):d.value.length===0?(o(),i("div",xe,t[14]||(t[14]=[e("i",{class:"fas fa-images text-5xl mb-4"},null,-1),e("p",null,"暫無輪播圖資料",-1)]))):(o(),i("div",be,[e("table",ye,[t[17]||(t[17]=e("thead",{class:"bg-gray-50"},[e("tr",null,[e("th",{scope:"col",class:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"}," 圖片 "),e("th",{scope:"col",class:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"}," 標題 "),e("th",{scope:"col",class:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"}," 連結 "),e("th",{scope:"col",class:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"}," 狀態 "),e("th",{scope:"col",class:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"}," 排序 "),e("th",{scope:"col",class:"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider"}," 操作 ")])],-1)),e("tbody",_e,[(o(!0),i(ee,null,te(d.value,a=>(o(),i("tr",{key:a.id},[e("td",we,[e("div",he,[e("img",{src:I(a.image_url),alt:"輪播圖",class:"h-full w-full object-cover rounded"},null,8,ke)])]),e("td",Ce,[e("div",Ae,g(a.title||"-"),1),a.description?(o(),i("div",Ue,g(a.description),1)):y("",!0)]),e("td",je,[a.link?(o(),i("div",Ve,[e("a",{href:a.link,target:"_blank",class:"hover:underline"},g(a.link),9,De)])):(o(),i("div",$e,"-"))]),e("td",Ie,[e("span",{class:D(["px-2 inline-flex text-xs leading-5 font-semibold rounded-full",a.is_active?"bg-green-100 text-green-800":"bg-red-100 text-red-800"])},g(a.is_active?"啟用":"停用"),3)]),e("td",Pe,g(a.sort_order),1),e("td",Re,[e("button",{onClick:r=>q(a),class:"text-blue-600 hover:text-blue-900 mr-3"},t[15]||(t[15]=[e("i",{class:"fas fa-edit mr-1"},null,-1),_(" 編輯 ")]),8,Ee),e("button",{onClick:r=>K(a),class:"text-red-600 hover:text-red-900"},t[16]||(t[16]=[e("i",{class:"fas fa-trash mr-1"},null,-1),_(" 刪除 ")]),8,Fe)])]))),128))])])]))])):y("",!0),j.value||b.value?(o(),i("div",Be,[e("div",Te,[e("h3",Le,g(b.value?"編輯輪播圖":"新增輪播圖"),1),e("div",Me,[e("div",Se,[t[23]||(t[23]=e("label",{class:"form-label"},[_("輪播圖片 "),e("span",{class:"text-red-500"},"*")],-1)),e("div",Ne,[c.value?(o(),i("div",ze,[e("div",Ge,[t[18]||(t[18]=e("p",{class:"text-sm text-gray-600 mb-2"},"請調整裁剪框大小和位置以選擇最佳顯示區域 (建議尺寸：寬度 500 像素 x 高度 200 像素)",-1)),e("div",Oe,[se(le(ie),{src:f.value,"stencil-props":z.value,onChange:O,class:"h-full"},null,8,["src","stencil-props"])]),t[19]||(t[19]=e("p",{class:"text-xs text-gray-500 mt-1"},"您可以自由調整裁剪框的大小和位置，建議尺寸為 500x200 像素",-1))]),e("div",{class:"flex justify-end space-x-2"},[e("button",{onClick:J,class:"btn-gray"},"取消"),e("button",{onClick:W,class:"btn-blue"},"確認裁剪")])])):(o(),i("div",We,[u.value?(o(),i("div",Je,[e("img",{src:u.value,alt:"預覽圖片",class:"h-full w-full object-cover"},null,8,He),e("div",{class:"absolute top-2 right-2 flex space-x-2"},[e("button",{onClick:H,class:"bg-blue-500 text-white rounded-full w-8 h-8 flex items-center justify-center",title:"編輯圖片"},t[20]||(t[20]=[e("i",{class:"fas fa-crop-alt"},null,-1)])),e("button",{onClick:P,class:"bg-red-500 text-white rounded-full w-8 h-8 flex items-center justify-center",title:"刪除圖片"},t[21]||(t[21]=[e("i",{class:"fas fa-times"},null,-1)]))])])):(o(),i("div",qe,[e("label",Ke,[t[22]||(t[22]=ae('<div class="text-gray-500 mb-2" data-v-ad72330c><i class="fas fa-cloud-upload-alt text-3xl" data-v-ad72330c></i></div><span class="text-gray-600" data-v-ad72330c>點擊上傳圖片</span><p class="text-xs text-gray-500 mt-1" data-v-ad72330c>支援格式：JPG, PNG, WEBP</p><p class="text-xs text-gray-500 mt-1" data-v-ad72330c>上傳後可裁剪圖片以獲得最佳顯示效果</p>',4)),e("input",{id:"fileUpload",ref_key:"fileUpload",ref:v,type:"file",accept:"image/*",class:"hidden",onChange:G},null,544)])]))]))]),m.image?(o(),i("small",Qe,g(m.image),1)):y("",!0)]),e("div",Xe,[t[24]||(t[24]=e("label",{for:"carouselTitle",class:"form-label"},"標題",-1)),w(e("input",{id:"carouselTitle","onUpdate:modelValue":t[3]||(t[3]=a=>l.title=a),type:"text",class:"form-input",placeholder:"輸入輪播圖標題"},null,512),[[A,l.title]])]),e("div",Ye,[t[25]||(t[25]=e("label",{for:"carouselDescription",class:"form-label"},"描述",-1)),w(e("textarea",{id:"carouselDescription","onUpdate:modelValue":t[4]||(t[4]=a=>l.description=a),class:"form-textarea",rows:"2",placeholder:"輸入輪播圖描述"},null,512),[[A,l.description]])]),e("div",Ze,[t[26]||(t[26]=e("label",{for:"carouselLink",class:"form-label"},"連結網址",-1)),w(e("input",{id:"carouselLink","onUpdate:modelValue":t[5]||(t[5]=a=>l.link=a),type:"url",class:"form-input",placeholder:"https://example.com"},null,512),[[A,l.link]]),t[27]||(t[27]=e("small",{class:"text-gray-500"},"點擊圖片後將跳轉的連結，若不設置則圖片不可點擊",-1))]),e("div",et,[e("div",tt,[t[28]||(t[28]=e("label",{for:"carouselOrder",class:"form-label"},"排序順序",-1)),w(e("input",{id:"carouselOrder","onUpdate:modelValue":t[6]||(t[6]=a=>l.sort_order=a),type:"number",min:"0",class:"form-input"},null,512),[[A,l.sort_order,void 0,{number:!0}]]),t[29]||(t[29]=e("small",{class:"text-gray-500"},"數字越小排序越前面",-1))]),e("div",st,[t[32]||(t[32]=e("label",{class:"form-label"},"狀態",-1)),e("div",lt,[e("label",at,[w(e("input",{type:"radio","onUpdate:modelValue":t[7]||(t[7]=a=>l.is_active=a),value:1,class:"form-radio"},null,512),[[T,l.is_active]]),t[30]||(t[30]=e("span",{class:"ml-2"},"啟用",-1))]),e("label",ot,[w(e("input",{type:"radio","onUpdate:modelValue":t[8]||(t[8]=a=>l.is_active=a),value:0,class:"form-radio"},null,512),[[T,l.is_active]]),t[31]||(t[31]=e("span",{class:"ml-2"},"停用",-1))])])])])]),e("div",it,[e("button",{onClick:R,class:"btn-gray"},"取消"),e("button",{onClick:X,class:"btn-blue",disabled:k.value},[k.value?(o(),i("span",nt,"儲存中...")):(o(),i("span",dt,t[33]||(t[33]=[e("i",{class:"fas fa-save mr-2"},null,-1),_(" 儲存")])))],8,rt)])])])):y("",!0),C.value?(o(),i("div",ut,[e("div",ct,[t[35]||(t[35]=e("h3",{class:"text-xl font-semibold mb-4"},"確認刪除",-1)),t[36]||(t[36]=e("p",null,"您確定要刪除這張輪播圖嗎？此操作無法還原。",-1)),e("div",pt,[e("button",{onClick:t[9]||(t[9]=a=>C.value=!1),class:"btn-gray"},"取消"),e("button",{onClick:Q,class:"btn-red"},t[34]||(t[34]=[e("i",{class:"fas fa-trash mr-2"},null,-1),_(" 確認刪除 ")]))])])])):y("",!0)]))}},ht=de(mt,[["__scopeId","data-v-ad72330c"]]);export{ht as default};
