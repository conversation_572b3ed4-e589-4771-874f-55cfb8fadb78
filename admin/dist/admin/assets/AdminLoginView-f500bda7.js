import{_ as b,f as u,r as w,U as i,V as d,$ as s,B as h,m as g,a0 as f,a1 as I,a2 as S}from"./vendor-91c90871.js";import{a as k}from"./utils-95cec1c7.js";import{g as V}from"./apiConfig-ad0108da.js";import{_ as T}from"./index-928ecde2.js";import{m as L}from"./ant-design-48c6fae4.js";const A={class:"admin-login-container"},U={class:"admin-login-box"},B={class:"form-group"},q={class:"form-group"},E={key:0,class:"error-message"},N=["disabled"],j={key:0},C={key:1},D={__name:"AdminLoginView",setup(M){const v=b(),r=u(!1),t=u(""),a=w({mail:"",password:""}),_=async()=>{var m,e,l,c;r.value=!0,t.value="";try{localStorage.removeItem("adminLogin"),localStorage.removeItem("adminToken"),localStorage.removeItem("adminInfo"),console.log("嘗試登入管理員後台"),console.log("登入資訊:",{mail:a.mail,password:"******"});const p=V("admin/login");console.log("發送登入請求到:",p);const n=await k.post(p,{mail:a.mail,password:a.password},{headers:{"Content-Type":"application/json",Accept:"application/json"}});if(console.log("登入響應:",n.data),n.data&&n.data.success){const{token:x,admin:y}=n.data;localStorage.setItem("adminToken",x),localStorage.setItem("adminLogin","true"),localStorage.setItem("adminInfo",JSON.stringify(y)),console.log("登入成功，已保存登入憑證"),L.success("管理員登入成功"),setTimeout(()=>{v.push("/")},500);return}else t.value=n.data.message||"登入失敗"}catch(o){console.error("登入錯誤:",o),console.error("錯誤詳情:",{status:(m=o.response)==null?void 0:m.status,statusText:(e=o.response)==null?void 0:e.statusText,data:(l=o.response)==null?void 0:l.data,headers:(c=o.response)==null?void 0:c.headers}),o.response?o.response.status===401?t.value="管理員帳號或密碼錯誤":o.response.status===403?t.value="您沒有管理員權限":t.value=o.response.data.message||"登入失敗，請稍後再試":o.request?t.value="無法連接到伺服器，請檢查網路連接":t.value="登入過程發生錯誤，請稍後再試"}finally{r.value=!1}};return(m,e)=>(i(),d("div",A,[s("div",U,[e[4]||(e[4]=s("div",{class:"text-center mb-6"},[s("h1",{class:"text-2xl font-bold text-gray-800"},"管理員後台登入"),s("p",{class:"text-gray-600"},"請輸入您的管理員帳號和密碼")],-1)),s("form",{onSubmit:h(_,["prevent"]),class:"space-y-4"},[s("div",B,[e[2]||(e[2]=s("label",{for:"account",class:"form-label"},"管理員帳號",-1)),g(s("input",{id:"account","onUpdate:modelValue":e[0]||(e[0]=l=>a.mail=l),type:"text",class:"form-input",placeholder:"請輸入管理員帳號",required:""},null,512),[[f,a.mail]])]),s("div",q,[e[3]||(e[3]=s("label",{for:"password",class:"form-label"},"密碼",-1)),g(s("input",{id:"password","onUpdate:modelValue":e[1]||(e[1]=l=>a.password=l),type:"password",class:"form-input",placeholder:"請輸入密碼",required:""},null,512),[[f,a.password]])]),t.value?(i(),d("div",E,I(t.value),1)):S("",!0),s("button",{type:"submit",class:"login-button",disabled:r.value},[r.value?(i(),d("span",j,"登入中...")):(i(),d("span",C,"管理員登入"))],8,N)],32),e[5]||(e[5]=s("div",{class:"text-center mt-4"},[s("a",{href:"/",class:"text-gray-600 text-sm hover:text-blue-600"},"返回前台首頁")],-1))])]))}},z=T(D,[["__scopeId","data-v-65c57e4b"]]);export{z as default};
