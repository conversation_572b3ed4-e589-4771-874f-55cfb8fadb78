import{f as y,r as B,c as U,o as Q,U as o,V as a,$ as t,m as A,a0 as R,a7 as X,a1 as d,G as m,a2 as x,F as I,a6 as F,a5 as h,ad as Y}from"./vendor-91c90871.js";import{a as Z}from"./api-a2dfd2a1.js";import{_ as ee}from"./index-928ecde2.js";import{m as v}from"./ant-design-48c6fae4.js";import"./utils-95cec1c7.js";import"./apiConfig-ad0108da.js";const te={class:"users-admin"},se={class:"bg-white p-4 rounded-lg shadow mb-6"},le={class:"grid grid-cols-1 md:grid-cols-2 gap-4"},ne={class:"bg-white rounded-lg shadow overflow-hidden"},oe={key:0,class:"p-6 text-center"},ae={key:1,class:"p-6 text-center text-gray-500"},ie={key:2},re={key:0,class:"mb-4 px-6 pt-4"},de={class:"flex space-x-2"},ce={class:"self-center mr-2 text-sm text-gray-600"},ue={class:"overflow-x-auto"},fe={class:"min-w-full divide-y divide-gray-200"},ve={class:"bg-gray-50"},pe={class:"th-checkbox"},me=["checked"],ge={class:"bg-white divide-y divide-gray-200"},be={class:"px-4 py-4 text-center"},xe=["value","disabled"],ye={class:"px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900"},he=["onClick"],we={key:0,class:"fas fa-crown admin-crown ml-1",title:"管理員"},_e={class:"px-6 py-4 whitespace-nowrap text-sm text-gray-500"},ke={class:"px-6 py-4 whitespace-nowrap text-sm text-gray-500"},Ce={class:"px-6 py-4 whitespace-nowrap text-sm text-gray-500"},$e={key:0,class:"admin-badge"},Ue={key:1,class:"member-badge"},Ae={class:"px-6 py-4 whitespace-nowrap text-sm text-gray-500"},Se={class:"px-6 py-4 whitespace-nowrap"},Me={class:"px-6 py-4 whitespace-nowrap text-sm text-gray-500"},De={class:"flex space-x-2"},ze=["onClick"],Pe=["onClick"],Ve=["onClick"],Te={key:2,class:"protected-icon",title:"管理員帳號受保護"},je={class:"px-6 py-3 flex justify-between items-center border-t"},Ne={class:"text-sm text-gray-500"},Be={class:"flex items-center space-x-2"},Ie=["disabled"],Fe=["onClick"],Le=["disabled"],Ee={key:0,class:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50"},Ge={class:"bg-white rounded-lg shadow-lg w-full max-w-xl p-6"},We={class:"flex justify-between items-center mb-4"},qe={key:0,class:"space-y-4"},He={class:"grid grid-cols-2 gap-4"},Je={class:"info-group"},Ke={class:"info-value"},Oe={class:"info-group"},Qe={class:"info-value"},Re={class:"info-group"},Xe={class:"info-value"},Ye={class:"info-group"},Ze={class:"info-value"},et={class:"info-group"},tt={class:"info-value"},st={key:0,class:"admin-badge"},lt={key:1,class:"member-badge"},nt={class:"info-group"},ot={class:"info-value"},at={class:"border-t pt-4 flex justify-end space-x-3"},it={key:2,class:"admin-protected-message"},rt={__name:"UsersView",setup(dt){const{getAdminApiData:L,postAdminApiData:S,deleteAdminApiData:M}=Z(),f=y([]),_=y(!0),c=y(null),b=y(!1),p=B({keyword:"",status:""}),l=B({current:1,pageSize:10,total:0,totalPages:1,start:1,end:10}),E=U(()=>{const s=l.totalPages,e=l.current;return s<=5?Array.from({length:s},(n,i)=>i+1):e<=3?[1,2,3,4,5]:e>=s-2?[s-4,s-3,s-2,s-1,s]:[e-2,e-1,e,e+1,e+2]}),g=async()=>{_.value=!0;try{const s={page:l.current,pageSize:l.pageSize};p.keyword&&(s.keyword=p.keyword),p.status&&(s.status=p.status);const e=await L("admin/users",s);e.success?(f.value=e.users||[],l.total=e.total||0,l.totalPages=Math.ceil(l.total/l.pageSize),l.start=(l.current-1)*l.pageSize+1,l.end=Math.min(l.current*l.pageSize,l.total)):console.error("載入會員失敗:",e.message)}catch(s){console.error("載入會員出錯:",s)}finally{_.value=!1}},k=s=>{s<1||s>l.totalPages||(l.current=s,g())},D=s=>s?new Date(s).toLocaleDateString("zh-TW"):"-",z=async s=>{c.value={...s},b.value=!0},P=s=>({pending:"未審核",active:"啟用",inactive:"停用"})[s]||"未知",V=s=>({pending:"inline-flex px-2 py-1 rounded-full text-xs bg-yellow-100 text-yellow-800",active:"inline-flex px-2 py-1 rounded-full text-xs bg-green-100 text-green-800",inactive:"inline-flex px-2 py-1 rounded-full text-xs bg-red-100 text-red-800"})[s]||"",r=y([]),C=U(()=>f.value.filter(s=>s.role!=="admin")),$=U({get:()=>C.value.length>0&&r.value.length===C.value.length,set:s=>{r.value=s?C.value.map(e=>e.id):[]}}),G=()=>{$.value=!$.value},T=async s=>{if(confirm(`確定要審核通過會員「${s.name}」嗎？`))try{const e=await S("admin/users/approve",{id:s.id});e.success?(s.status="active",alert("會員已審核通過")):alert(`操作失敗: ${e.message}`)}catch(e){console.error("審核會員出錯:",e),alert("審核會員時發生錯誤，請稍後再試")}},W=async()=>{if(r.value.length===0){alert("請選擇要審核的會員");return}if(confirm(`確定要審核通過所選的 ${r.value.length} 位會員嗎？`))try{const s=await S("admin/users/batch-approve",{ids:r.value});if(s.success){for(const e of r.value){const n=f.value.find(i=>i.id===e);n&&n.status==="pending"&&(n.status="active")}alert("所選會員已審核通過"),r.value=[]}else alert(`操作失敗: ${s.message}`)}catch(s){console.error("批量審核會員出錯:",s),alert("批量審核會員時發生錯誤，請稍後再試")}};Q(()=>{g()});let j=null;const q=()=>{clearTimeout(j),j=setTimeout(()=>{g()},500)},H=s=>{T(s),b.value=!1},J=s=>{b.value=!1,N(s)},N=async s=>{if(s.role==="admin"){v.error("無法刪除管理員帳號");return}if(!(!window.confirm(`⚠️ 警告：您即將刪除會員「${s.name}」

此操作將：
• 停用該會員帳號
• 保留相關訂單和評論資料
• 無法恢復帳號狀態

確定要繼續嗎？`)||!window.confirm(`最後確認：確定要刪除會員「${s.name}」嗎？`)))try{const i=await M("admin/users/delete",{id:s.id});i.success?(v.success("會員已成功刪除"),f.value=f.value.filter(u=>u.id!==s.id),l.total-=1,l.totalPages=Math.ceil(l.total/l.pageSize),f.value.length===0&&l.current>1&&(l.current-=1,g())):v.error(`刪除失敗: ${i.message}`)}catch(i){console.error("刪除會員出錯:",i),v.error("刪除會員時發生錯誤，請稍後再試")}},K=async()=>{if(r.value.length===0){v.warning("請選擇要刪除的會員");return}const s=r.value.filter(u=>{const w=f.value.find(O=>O.id===u);return w&&w.role!=="admin"});if(s.length===0){v.warning("選中的會員中沒有可刪除的帳號");return}s.length!==r.value.length&&v.warning("已自動排除管理員帳號，只會刪除一般會員");const e=f.value.filter(u=>s.includes(u.id)).map(u=>u.name).join("、");if(!(!window.confirm(`⚠️ 警告：您即將批量刪除 ${s.length} 位會員

會員名單：${e}

此操作將：
• 停用這些會員帳號
• 保留相關訂單和評論資料
• 無法恢復帳號狀態

確定要繼續嗎？`)||!window.confirm(`最後確認：確定要批量刪除這 ${s.length} 位會員嗎？`)))try{const u=await M("admin/users/batch-delete",{ids:s});u.success?(v.success(`已成功刪除 ${u.deletedCount} 位會員`),f.value=f.value.filter(w=>!s.includes(w.id)),l.total-=u.deletedCount,l.totalPages=Math.ceil(l.total/l.pageSize),r.value=[],f.value.length===0&&l.current>1&&(l.current-=1),g()):v.error(`批量刪除失敗: ${u.message}`)}catch(u){console.error("批量刪除會員出錯:",u),v.error("批量刪除會員時發生錯誤，請稍後再試")}};return(s,e)=>(o(),a("div",te,[t("div",se,[t("div",le,[t("div",null,[e[9]||(e[9]=t("label",{class:"block text-sm font-medium text-gray-700 mb-1"},"會員名稱/信箱",-1)),A(t("input",{type:"text","onUpdate:modelValue":e[0]||(e[0]=n=>p.keyword=n),placeholder:"搜尋會員名稱或電子郵件",class:"form-input",onInput:q},null,544),[[R,p.keyword]])]),t("div",null,[e[11]||(e[11]=t("label",{class:"block text-sm font-medium text-gray-700 mb-1"},"會員狀態",-1)),A(t("select",{"onUpdate:modelValue":e[1]||(e[1]=n=>p.status=n),class:"form-select",onChange:g},e[10]||(e[10]=[t("option",{value:""},"全部狀態",-1),t("option",{value:"pending"},"未審核",-1),t("option",{value:"active"},"啟用",-1),t("option",{value:"inactive"},"停用",-1)]),544),[[X,p.status]])])])]),t("div",ne,[_.value?(o(),a("div",oe,e[12]||(e[12]=[t("div",{class:"spinner"},null,-1),t("p",{class:"mt-2 text-gray-600"},"載入中...",-1)]))):f.value.length===0?(o(),a("div",ae,e[13]||(e[13]=[t("i",{class:"fas fa-users text-5xl mb-4"},null,-1),t("p",null,"暫無會員資料",-1)]))):(o(),a("div",ie,[r.value.length>0?(o(),a("div",re,[t("div",de,[t("span",ce,"已選擇 "+d(r.value.length)+" 位會員",1),t("button",{onClick:W,class:"btn-green-sm"},e[14]||(e[14]=[t("i",{class:"fas fa-check-circle mr-1"},null,-1),m(" 批量審核通過 ")])),t("button",{onClick:K,class:"btn-red-sm"},e[15]||(e[15]=[t("i",{class:"fas fa-trash mr-1"},null,-1),m(" 批量刪除 ")]))])])):x("",!0),t("div",ue,[t("table",fe,[t("thead",ve,[t("tr",null,[t("th",pe,[t("input",{type:"checkbox",class:"form-checkbox rounded",checked:$.value,onChange:G},null,40,me)]),e[16]||(e[16]=t("th",{class:"th"},"姓名",-1)),e[17]||(e[17]=t("th",{class:"th"},"帳號",-1)),e[18]||(e[18]=t("th",{class:"th"},"電話",-1)),e[19]||(e[19]=t("th",{class:"th"},"角色",-1)),e[20]||(e[20]=t("th",{class:"th"},"註冊日期",-1)),e[21]||(e[21]=t("th",{class:"th"},"狀態",-1)),e[22]||(e[22]=t("th",{class:"th"},"操作",-1))])]),t("tbody",ge,[(o(!0),a(I,null,F(f.value,n=>(o(),a("tr",{key:n.id,class:"hover:bg-gray-50"},[t("td",be,[A(t("input",{type:"checkbox",class:"form-checkbox rounded","onUpdate:modelValue":e[2]||(e[2]=i=>r.value=i),value:n.id,disabled:n.role==="admin"},null,8,xe),[[Y,r.value]])]),t("td",ye,[t("button",{onClick:i=>z(n),class:"text-blue-600 hover:text-blue-900"},[m(d(n.name)+" ",1),n.role==="admin"?(o(),a("i",we)):x("",!0)],8,he)]),t("td",_e,d(n.mail),1),t("td",ke,d(n.phone),1),t("td",Ce,[n.role==="admin"?(o(),a("span",$e,e[23]||(e[23]=[t("i",{class:"fas fa-crown mr-1"},null,-1),m(" 管理員 ")]))):(o(),a("span",Ue," 一般會員 "))]),t("td",Ae,d(D(n.createdAt)),1),t("td",Se,[t("span",{class:h(V(n.status))},d(P(n.status)),3)]),t("td",Me,[t("div",De,[t("button",{onClick:i=>z(n),class:"text-blue-600 hover:text-blue-900",title:"查看詳情"},e[24]||(e[24]=[t("i",{class:"fas fa-eye"},null,-1)]),8,ze),n.status==="pending"&&n.role!=="admin"?(o(),a("button",{key:0,onClick:i=>T(n),class:"text-green-600 hover:text-green-900",title:"審核通過"},e[25]||(e[25]=[t("i",{class:"fas fa-check"},null,-1)]),8,Pe)):x("",!0),n.role!=="admin"?(o(),a("button",{key:1,onClick:i=>N(n),class:"text-red-600 hover:text-red-900",title:"刪除會員"},e[26]||(e[26]=[t("i",{class:"fas fa-trash"},null,-1)]),8,Ve)):(o(),a("span",Te,e[27]||(e[27]=[t("i",{class:"fas fa-shield-alt"},null,-1)])))])])]))),128))])])]),t("div",je,[t("div",Ne," 顯示 "+d(l.start)+"-"+d(l.end)+" 筆，共 "+d(l.total)+" 筆 ",1),t("div",Be,[t("button",{onClick:e[3]||(e[3]=n=>k(l.current-1)),disabled:l.current===1,class:h(["btn-gray-sm",{"opacity-50 cursor-not-allowed":l.current===1}])},e[28]||(e[28]=[t("i",{class:"fas fa-chevron-left"},null,-1)]),10,Ie),(o(!0),a(I,null,F(E.value,n=>(o(),a("span",{key:n,class:h(["page-number",{"bg-blue-600 text-white":n===l.current}]),onClick:i=>k(n)},d(n),11,Fe))),128)),t("button",{onClick:e[4]||(e[4]=n=>k(l.current+1)),disabled:l.current===l.totalPages,class:h(["btn-gray-sm",{"opacity-50 cursor-not-allowed":l.current===l.totalPages}])},e[29]||(e[29]=[t("i",{class:"fas fa-chevron-right"},null,-1)]),10,Le)])])]))]),b.value?(o(),a("div",Ee,[t("div",Ge,[t("div",We,[e[31]||(e[31]=t("h3",{class:"text-xl font-semibold"},"會員詳情",-1)),t("button",{onClick:e[5]||(e[5]=n=>b.value=!1),class:"text-gray-500 hover:text-gray-700"},e[30]||(e[30]=[t("i",{class:"fas fa-times"},null,-1)]))]),c.value?(o(),a("div",qe,[t("div",He,[t("div",Je,[e[32]||(e[32]=t("label",{class:"info-label"},"姓名",-1)),t("div",Ke,d(c.value.name),1)]),t("div",Oe,[e[33]||(e[33]=t("label",{class:"info-label"},"電話",-1)),t("div",Qe,d(c.value.phone||"未設定"),1)]),t("div",Re,[e[34]||(e[34]=t("label",{class:"info-label"},"生日",-1)),t("div",Xe,d(c.value.birthday||"未設定"),1)]),t("div",Ye,[e[35]||(e[35]=t("label",{class:"info-label"},"註冊日期",-1)),t("div",Ze,d(D(c.value.createdAt)),1)]),t("div",et,[e[37]||(e[37]=t("label",{class:"info-label"},"角色",-1)),t("div",tt,[c.value.role==="admin"?(o(),a("span",st,e[36]||(e[36]=[t("i",{class:"fas fa-crown mr-1"},null,-1),m(" 管理員 ")]))):(o(),a("span",lt," 一般會員 "))])]),t("div",nt,[e[38]||(e[38]=t("label",{class:"info-label"},"狀態",-1)),t("div",ot,[t("span",{class:h(V(c.value.status))},d(P(c.value.status)),3)])])]),t("div",at,[t("button",{onClick:e[6]||(e[6]=n=>b.value=!1),class:"btn-gray"},"關閉"),c.value.status==="pending"&&c.value.role!=="admin"?(o(),a("button",{key:0,onClick:e[7]||(e[7]=n=>H(c.value)),class:"btn-green"},e[39]||(e[39]=[t("i",{class:"fas fa-check mr-2"},null,-1),m(" 審核通過 ")]))):x("",!0),c.value.role!=="admin"?(o(),a("button",{key:1,onClick:e[8]||(e[8]=n=>J(c.value)),class:"btn-red"},e[40]||(e[40]=[t("i",{class:"fas fa-trash mr-2"},null,-1),m(" 刪除會員 ")]))):(o(),a("div",it,e[41]||(e[41]=[t("i",{class:"fas fa-shield-alt mr-2"},null,-1),m(" 管理員帳號受保護，無法刪除 ")])))])])):x("",!0)])])):x("",!0)]))}},gt=ee(rt,[["__scopeId","data-v-ded70bf6"]]);export{gt as default};
