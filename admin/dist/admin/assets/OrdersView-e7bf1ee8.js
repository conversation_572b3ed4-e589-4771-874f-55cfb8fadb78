import{f as p,r as x,c as lt,o as $t,U as o,V as n,$ as e,a1 as i,G as m,a2 as f,m as g,a0 as T,a7 as Y,aa as It,F as J,a6 as G,a5 as _,B as St,ad as Dt}from"./vendor-91c90871.js";import{a as jt}from"./api-a2dfd2a1.js";import{g as at,a as ot}from"./apiConfig-ad0108da.js";import{d as B}from"./ant-design-48c6fae4.js";import{a as nt}from"./utils-95cec1c7.js";import{_ as Mt}from"./index-928ecde2.js";const Vt={class:"orders-admin"},At={class:"mb-6 flex justify-end items-center"},Pt={key:0,class:"flex space-x-2"},zt={class:"self-center mr-2 text-sm text-gray-600"},Nt={class:"bg-white p-4 rounded-lg shadow mb-6"},Ut={class:"grid grid-cols-1 md:grid-cols-4 gap-4"},Tt={class:"bg-white rounded-lg shadow overflow-hidden"},Yt={key:0,class:"p-6 text-center"},Bt={key:1,class:"p-6 text-center text-gray-500"},Ot={key:2},Rt={class:"overflow-x-auto"},qt={class:"min-w-full divide-y divide-gray-200"},Ft={class:"bg-gray-50"},Et={class:"th-checkbox"},Lt=["checked"],Jt={class:"bg-white divide-y divide-gray-200"},Gt={class:"px-4 py-4 text-center"},Ht=["value"],Kt={class:"px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900"},Qt={class:"px-6 py-4 whitespace-nowrap text-sm text-gray-500"},Wt={class:"px-6 py-4 whitespace-nowrap text-sm text-gray-500"},Xt={class:"px-6 py-4 whitespace-nowrap text-sm text-gray-500"},Zt={class:"px-6 py-4 whitespace-nowrap text-sm text-gray-500"},te={class:"px-6 py-4 whitespace-nowrap text-sm text-gray-500"},ee={class:"px-6 py-4 whitespace-nowrap"},se={class:"px-6 py-4 whitespace-nowrap text-sm text-gray-500"},le={class:"flex items-center space-x-2"},ae={key:0},oe={key:1,class:"text-gray-400"},ne=["onClick"],ie={class:"px-6 py-4 whitespace-nowrap"},re={class:"px-6 py-4 whitespace-nowrap text-sm text-gray-500"},de={class:"flex items-center space-x-1"},ue=["onClick"],ce=["onClick"],ve=["onClick"],fe={class:"px-6 py-3 flex justify-between items-center border-t"},pe={class:"text-sm text-gray-500"},me={class:"flex items-center space-x-2"},ye=["disabled"],ge=["onClick"],be=["disabled"],xe={class:"bg-white rounded-lg shadow-lg w-full max-w-3xl p-6 max-h-[90vh] overflow-y-auto"},_e={class:"flex justify-between items-center mb-4 sticky top-0 bg-white z-10 pb-2"},he={key:0,class:"space-y-6"},we={class:"grid grid-cols-2 md:grid-cols-3 gap-4"},ke={class:"info-group"},Ce={class:"info-value"},$e={class:"info-group"},Ie={class:"info-value"},Se={class:"info-group"},De={class:"info-value"},je={class:"info-group"},Me={class:"info-value"},Ve={class:"info-group"},Ae={class:"info-value"},Pe={key:0,class:"text-xs text-gray-500 ml-2"},ze={class:"info-group"},Ne={class:"info-value"},Ue={key:0},Te={key:1,class:"text-gray-400"},Ye={class:"info-group"},Be={class:"info-value"},Oe={class:"info-group"},Re={class:"info-value"},qe={class:"info-group"},Fe={class:"info-value"},Ee={class:"info-group"},Le={class:"info-value"},Je={class:"info-group"},Ge={class:"info-value"},He={class:"info-group"},Ke={class:"info-value"},Qe={class:"info-group"},We={class:"info-value"},Xe={class:"info-group"},Ze={class:"info-value"},ts={class:"info-group col-span-2 md:col-span-3"},es={class:"info-value"},ss={key:0,class:"mb-3 p-3 bg-yellow-50 border border-yellow-200 rounded-lg flex items-start"},ls={class:"border rounded-lg overflow-hidden"},as={class:"min-w-full divide-y divide-gray-200"},os={class:"bg-white divide-y divide-gray-200"},ns={class:"px-4 py-3"},is={class:"w-16 h-16 bg-gray-200 rounded overflow-hidden relative"},rs=["src","alt"],ds={key:0,class:"absolute inset-0 flex items-center justify-center text-xs text-gray-500"},us={class:"px-4 py-3 text-sm"},cs={class:"px-4 py-3 text-sm"},vs={class:"px-4 py-3 text-sm"},fs={key:0},ps={key:1,class:"text-gray-400 italic"},ms={class:"px-4 py-3 text-sm"},ys={class:"flex justify-end border-t pt-4"},gs={class:"w-64"},bs={class:"flex justify-between mb-2"},xs={class:"flex justify-between font-semibold text-lg"},_s={class:"flex justify-end space-x-3 border-t pt-4"},hs={key:1,class:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50"},ws={class:"bg-white rounded-lg shadow-lg w-full max-w-md p-6"},ks={class:"flex justify-between items-center mb-4"},Cs={key:0,class:"space-y-4"},$s={class:"info-group"},Is={class:"info-value"},Ss={class:"form-group"},Ds={class:"mt-6 flex justify-end space-x-3"},js=["disabled"],Ms={key:0},Vs={key:1},As={key:2,class:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50"},Ps={class:"bg-white rounded-lg shadow-lg w-full max-w-md p-6"},zs={class:"flex justify-between items-center mb-4"},Ns={key:0,class:"space-y-4"},Us={class:"info-group"},Ts={class:"info-value"},Ys={class:"form-group"},Bs={class:"mt-6 flex justify-end space-x-3"},Os=["disabled"],Rs={key:0},qs={key:1},Fs={key:3,class:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50"},Es={class:"bg-white rounded-lg shadow-lg w-full max-w-md p-6"},Ls={class:"flex justify-between items-center mb-4"},Js={class:"space-y-4"},Gs={class:"text-gray-600"},Hs={class:"form-group"},Ks={class:"mt-6 flex justify-end space-x-3"},Qs=["disabled"],Ws={key:0},Xs={key:1},Zs={key:4,class:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50"},tl={class:"bg-white rounded-lg shadow-lg w-full max-w-md p-6"},el={class:"flex justify-between items-center mb-4"},sl={class:"space-y-4"},ll={class:"text-gray-700"},al={class:"mt-6 flex justify-end space-x-3"},ol=["disabled"],nl={key:0},il={key:1},rl={key:5,class:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50"},dl={class:"bg-white rounded-lg shadow-lg w-full max-w-md p-6"},ul={class:"flex justify-between items-center mb-4"},cl={class:"space-y-4"},vl={class:"form-group"},fl={class:"mt-6 flex justify-end space-x-3"},pl=["disabled"],ml={key:0},yl={key:1},gl={key:6,class:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50"},bl={class:"bg-white rounded-lg shadow-lg w-full max-w-md p-6"},xl={class:"flex justify-between items-center mb-4"},_l={class:"space-y-4"},hl={class:"text-gray-600"},wl={class:"form-group"},kl={class:"mt-6 flex justify-end space-x-3"},Cl=["disabled"],$l={key:0},Il={key:1},Sl={__name:"OrdersView",setup(Dl){const{getAdminApiData:jl,postAdminApiData:it,updateAdminApiData:k}=jt(),v=p([]),O=p(!0),a=p(null),z=p([]),C=p(!1),$=p(!1),I=p(!1),S=p(!1),D=p(!1),j=p(!1),M=p(!1),u=p(!1),y=x({orderId:"",client:"",status:""}),r=x({current:1,pageSize:10,total:0,totalPages:1,start:1,end:10}),h=x({status:"1"}),w=x({payment_status:"0"}),N=x({status:"1"}),b=x({remittance_date:""}),V=x({remittance_date:""}),c=p([]),R=lt({get:()=>v.value.length>0&&c.value.length===v.value.length,set:l=>{c.value=l?v.value.map(t=>t.id):[]}}),rt=()=>{R.value=!R.value},dt=lt(()=>{const l=r.totalPages,t=r.current;return l<=5?Array.from({length:l},(s,d)=>d+1):t<=3?[1,2,3,4,5]:t>=l-2?[l-4,l-3,l-2,l-1,l]:[t-2,t-1,t,t+1,t+2]});let q=null;const H=()=>{q&&clearTimeout(q),q=setTimeout(()=>{r.current=1,A()},300)},A=async()=>{var l;O.value=!0;try{const t={page:r.current,pageSize:r.pageSize};y.orderId&&(t.orderId=y.orderId),y.client&&(t.client=y.client),y.status&&(t.status=y.status);const s=await nt.get(at("admin/orders"),{params:t,headers:{"Content-Type":"application/json",Authorization:`Bearer ${localStorage.getItem("adminToken")}`}});console.log("訂單API響應:",s.data),s.data&&s.data.success?(v.value=s.data.orders||[],r.total=s.data.total||0,r.totalPages=Math.ceil(r.total/r.pageSize),r.start=(r.current-1)*r.pageSize+1,r.end=Math.min(r.current*r.pageSize,r.total)):(console.error("載入訂單失敗:",(l=s.data)==null?void 0:l.message),v.value=[],r.total=0,r.totalPages=0,r.start=0,r.end=0)}catch(t){console.error("載入訂單出錯:",t),v.value=[],r.total=0,r.totalPages=0,r.start=0,r.end=0}finally{O.value=!1}},F=l=>{l<1||l>r.totalPages||(r.current=l,A())},K=l=>l===1?"已付款":"未付款",Q=l=>l===1?"px-2 py-1 rounded-full text-xs bg-green-100 text-green-800":"px-2 py-1 rounded-full text-xs bg-yellow-100 text-yellow-800",W=l=>({1:"處理中",2:"已出貨",3:"已完成",4:"已取消",1:"處理中",2:"已出貨",3:"已完成",4:"已取消"})[l]||"未知",X=l=>({1:"px-2 py-1 rounded-full text-xs bg-yellow-100 text-yellow-800",2:"px-2 py-1 rounded-full text-xs bg-blue-100 text-blue-800",3:"px-2 py-1 rounded-full text-xs bg-green-100 text-green-800",4:"px-2 py-1 rounded-full text-xs bg-red-100 text-red-800",1:"px-2 py-1 rounded-full text-xs bg-yellow-100 text-yellow-800",2:"px-2 py-1 rounded-full text-xs bg-blue-100 text-blue-800",3:"px-2 py-1 rounded-full text-xs bg-green-100 text-green-800",4:"px-2 py-1 rounded-full text-xs bg-red-100 text-red-800"})[l]||"",E=l=>{if(!l)return"-";const t=B(l);return t.isValid()?t.format("YYYY年M月D日 A h:mm"):l},Z=l=>{if(!l)return"-";if(l.length===10&&l.includes("-")){const s=B(l);if(s.isValid())return s.format("YYYY年M月D日")}const t=B(l);return t.isValid()?t.format("YYYY年M月D日"):l},P=l=>l==null?"0":l.toString().replace(/\B(?=(\d{3})+(?!\d))/g,","),ut=async l=>{a.value={...l};try{const t=await nt.get(at(`admin/orders/${l.id}`),{headers:{"Content-Type":"application/json",Authorization:`Bearer ${localStorage.getItem("adminToken")}`}});if(console.log("訂單詳情API響應:",t.data),t.data.success&&t.data.orderDetails){const s=t.data.orderDetails.products||[];z.value=s.map((d,st)=>{if(typeof d=="string")try{d=JSON.parse(d)}catch(Ct){return console.error("解析商品數據失敗:",Ct),d}let U=d.count||d.quantity||0;return U||(console.warn(`商品 "${d.name}" 缺少數量資訊`),U=null),{...d,count:U,quantity:U}}),console.log("處理後的訂單商品列表:",z.value),a.value={...a.value,...t.data.orderDetails}}else console.error("訂單詳情獲取失敗:",t.data.message)}catch(t){console.error("獲取訂單詳情錯誤:",t)}C.value=!0},tt=l=>{a.value={...l},h.status=l.status.toString(),$.value=!0},ct=async()=>{if(a.value){u.value=!0;try{const l=await k("admin/orders/status",{id:a.value.id,status:parseInt(h.status)});if(l.success){const t=v.value.findIndex(s=>s.id===a.value.id);t!==-1&&(v.value[t].status=parseInt(h.status)),a.value&&(a.value.status=parseInt(h.status)),$.value=!1,alert("訂單狀態已成功更新")}else alert(`更新失敗: ${l.message}`)}catch(l){console.error("更新訂單狀態出錯:",l),alert("更新訂單狀態時發生錯誤，請稍後再試")}finally{u.value=!1}}},vt=()=>{if(c.value.length===0){alert("請至少選擇一筆訂單");return}I.value=!0},ft=()=>{if(c.value.length===0){alert("請至少選擇一筆訂單");return}S.value=!0},pt=async()=>{if(c.value.length!==0){u.value=!0;try{const l=await k("admin/orders/batch-status",{ids:c.value,status:parseInt(N.status)});if(l.success){for(const t of c.value){const s=v.value.findIndex(d=>d.id===t);s!==-1&&(v.value[s].status=parseInt(N.status))}I.value=!1,c.value=[],alert("訂單狀態已成功更新")}else alert(`更新失敗: ${l.message}`)}catch(l){console.error("批量更新訂單狀態出錯:",l),alert("更新訂單狀態時發生錯誤，請稍後再試")}finally{u.value=!1}}},mt=async()=>{if(c.value.length!==0){u.value=!0;try{const l=await it("admin/orders/batch-delete",{ids:c.value});l.success?(c.value=[],S.value=!1,await A(),alert(l.message||"所選訂單已成功刪除")):alert(`刪除失敗: ${l.message}`)}catch(l){console.error("批量刪除訂單出錯:",l),alert("刪除訂單時發生錯誤，請稍後再試")}finally{u.value=!1}}},et=l=>{a.value={...l},w.payment_status=l.payment_status.toString(),D.value=!0},yt=l=>{a.value={...l},l.remittance_date?b.remittance_date=B(l.remittance_date).format("YYYY-MM-DD"):b.remittance_date="",j.value=!0},gt=()=>{if(c.value.length===0){alert("請至少選擇一筆訂單");return}V.remittance_date="",M.value=!0},bt=async()=>{if(a.value){u.value=!0;try{const l=await k("admin/orders/payment-status",{id:a.value.id,payment_status:parseInt(w.payment_status)});if(l.success){const t=v.value.findIndex(s=>s.id===a.value.id);t!==-1&&(v.value[t].payment_status=parseInt(w.payment_status),v.value[t].payment_date=new Date().toISOString()),a.value&&(a.value.payment_status=parseInt(w.payment_status),a.value.payment_date=new Date().toISOString()),D.value=!1,alert("付款狀態已成功更新")}else alert(`更新失敗: ${l.message}`)}catch(l){console.error("更新付款狀態出錯:",l),alert("更新付款狀態時發生錯誤，請稍後再試")}finally{u.value=!1}}},xt=async()=>{if(a.value){u.value=!0;try{const l=await k("admin/orders/remittance-date",{id:a.value.id,remittance_date:b.remittance_date||null});if(l.success){const t=v.value.findIndex(s=>s.id===a.value.id);t!==-1&&(v.value[t].remittance_date=b.remittance_date||null),a.value&&(a.value.remittance_date=b.remittance_date||null),j.value=!1,alert("匯款日期已成功更新")}else alert(`更新失敗: ${l.message}`)}catch(l){console.error("更新匯款日期出錯:",l),alert("更新匯款日期時發生錯誤，請稍後再試")}finally{u.value=!1}}},_t=async()=>{if(c.value.length!==0){u.value=!0;try{const l=await k("admin/orders/batch-remittance-date",{ids:c.value,remittance_date:V.remittance_date||null});l.success?(c.value.forEach(t=>{const s=v.value.findIndex(d=>d.id===t);s!==-1&&(v.value[s].remittance_date=V.remittance_date||null)}),c.value=[],M.value=!1,alert("匯款日期已成功批量更新")):alert(`更新失敗: ${l.message}`)}catch(l){console.error("批量更新匯款日期出錯:",l),alert("批量更新匯款日期時發生錯誤，請稍後再試")}finally{u.value=!1}}},L=()=>"http://60.198.79.27:81/no-image.jpg",ht=l=>{console.log("圖片加載失敗:",l.target.src),l.target.style.display="none";const t=l.target.parentNode;if(t&&!t.querySelector(".error-text")){const s=document.createElement("span");s.textContent="無圖片",s.className="text-xs text-gray-500 error-text absolute inset-0 flex items-center justify-center",t.appendChild(s),console.log("圖片元素:",l.target),console.log("圖片父元素:",t),console.log("圖片URL:",l.target.src),console.log("圖片替代文字:",l.target.alt);const d=new Image;d.onload=()=>{l.target.src=d.src,l.target.style.display="block",t.querySelector(".error-text")&&t.querySelector(".error-text").remove()},d.onerror=()=>{console.log("靜態備用圖片也加載失敗")},d.src=L()}},wt=l=>{const t=parseFloat(l.price)||0,s=parseInt(l.count||l.quantity)||0;return s===0?t:t*s},kt=l=>{try{if(typeof l=="string")try{const t=JSON.parse(l);if(Array.isArray(t)&&t.length>0)return ot(t[0],"product")}catch{return ot(l,"product")}return L()}catch(t){return console.error("解析商品圖片時出錯:",t),L()}};return $t(()=>{A()}),(l,t)=>(o(),n("div",Vt,[e("div",At,[c.value.length>0?(o(),n("div",Pt,[e("span",zt,"已選擇 "+i(c.value.length)+" 筆訂單",1),e("button",{onClick:vt,class:"btn-blue-sm"},t[29]||(t[29]=[e("i",{class:"fas fa-edit mr-1"},null,-1),m(" 批量更新狀態 ")])),e("button",{onClick:gt,class:"btn-green-sm"},t[30]||(t[30]=[e("i",{class:"fas fa-calendar-alt mr-1"},null,-1),m(" 批量設定匯款日期 ")])),e("button",{onClick:ft,class:"btn-red-sm"},t[31]||(t[31]=[e("i",{class:"fas fa-trash-alt mr-1"},null,-1),m(" 批量刪除 ")]))])):f("",!0)]),e("div",Nt,[e("div",Ut,[e("div",null,[t[32]||(t[32]=e("label",{class:"block text-sm font-medium text-gray-700 mb-1"},"訂單編號",-1)),g(e("input",{type:"text","onUpdate:modelValue":t[0]||(t[0]=s=>y.orderId=s),placeholder:"搜尋訂單編號",class:"form-input",onInput:H},null,544),[[T,y.orderId]])]),e("div",null,[t[33]||(t[33]=e("label",{class:"block text-sm font-medium text-gray-700 mb-1"},"客戶姓名",-1)),g(e("input",{type:"text","onUpdate:modelValue":t[1]||(t[1]=s=>y.client=s),placeholder:"搜尋客戶姓名",class:"form-input",onInput:H},null,544),[[T,y.client]])]),e("div",null,[t[35]||(t[35]=e("label",{class:"block text-sm font-medium text-gray-700 mb-1"},"訂單狀態",-1)),g(e("select",{"onUpdate:modelValue":t[2]||(t[2]=s=>y.status=s),class:"form-select",onChange:A},t[34]||(t[34]=[It('<option value="" data-v-e50bd5c5>全部狀態</option><option value="1" data-v-e50bd5c5>處理中</option><option value="2" data-v-e50bd5c5>已出貨</option><option value="3" data-v-e50bd5c5>已完成</option><option value="4" data-v-e50bd5c5>已取消</option>',5)]),544),[[Y,y.status]])])])]),e("div",Tt,[O.value?(o(),n("div",Yt,t[36]||(t[36]=[e("div",{class:"spinner"},null,-1),e("p",{class:"mt-2 text-gray-600"},"載入中...",-1)]))):v.value.length===0?(o(),n("div",Bt,t[37]||(t[37]=[e("i",{class:"fas fa-shopping-cart text-5xl mb-4"},null,-1),e("p",null,"暫無訂單資料",-1)]))):(o(),n("div",Ot,[e("div",Rt,[e("table",qt,[e("thead",Ft,[e("tr",null,[e("th",Et,[e("input",{type:"checkbox",class:"form-checkbox rounded",checked:R.value,onChange:rt},null,40,Lt)]),t[38]||(t[38]=e("th",{class:"th"},"訂單編號",-1)),t[39]||(t[39]=e("th",{class:"th"},"客戶姓名",-1)),t[40]||(t[40]=e("th",{class:"th"},"訂購日期",-1)),t[41]||(t[41]=e("th",{class:"th"},"訂單金額",-1)),t[42]||(t[42]=e("th",{class:"th"},"訂購人",-1)),t[43]||(t[43]=e("th",{class:"th"},"聯絡電話",-1)),t[44]||(t[44]=e("th",{class:"th"},"付款狀態",-1)),t[45]||(t[45]=e("th",{class:"th"},"匯款日期",-1)),t[46]||(t[46]=e("th",{class:"th"},"訂單狀態",-1)),t[47]||(t[47]=e("th",{class:"th"},"操作",-1))])]),e("tbody",Jt,[(o(!0),n(J,null,G(v.value,s=>(o(),n("tr",{key:s.id,class:"hover:bg-gray-50"},[e("td",Gt,[g(e("input",{type:"checkbox",class:"form-checkbox rounded","onUpdate:modelValue":t[3]||(t[3]=d=>c.value=d),value:s.id},null,8,Ht),[[Dt,c.value]])]),e("td",Kt,i(s.orderId),1),e("td",Qt,i(s.clientName),1),e("td",Wt,i(E(s.createdAt)),1),e("td",Xt," NT$ "+i(P(s.total)),1),e("td",Zt,i(s.receiver),1),e("td",te,i(s.phone),1),e("td",ee,[e("span",{class:_(Q(s.payment_status))},i(K(s.payment_status)),3)]),e("td",se,[e("div",le,[s.remittance_date?(o(),n("span",ae,i(Z(s.remittance_date)),1)):(o(),n("span",oe,"未設定")),e("button",{onClick:d=>yt(s),class:"text-blue-600 hover:text-blue-800 text-xs",title:"設定匯款日期"},t[48]||(t[48]=[e("i",{class:"fas fa-calendar-alt"},null,-1)]),8,ne)])]),e("td",ie,[e("span",{class:_(X(s.status))},i(W(s.status)),3)]),e("td",re,[e("div",de,[e("button",{onClick:d=>ut(s),class:"action-btn view",title:"查看詳情"},t[49]||(t[49]=[e("i",{class:"fas fa-eye"},null,-1)]),8,ue),e("button",{onClick:d=>et(s),class:"action-btn payment",title:"更新付款狀態"},t[50]||(t[50]=[e("i",{class:"fas fa-money-check-alt"},null,-1)]),8,ce),e("button",{onClick:d=>tt(s),class:"action-btn edit",title:"更新訂單狀態"},t[51]||(t[51]=[e("i",{class:"fas fa-edit"},null,-1)]),8,ve)])])]))),128))])])]),e("div",fe,[e("div",pe," 顯示 "+i(r.start)+"-"+i(r.end)+" 筆，共 "+i(r.total)+" 筆 ",1),e("div",me,[e("button",{onClick:t[4]||(t[4]=s=>F(r.current-1)),disabled:r.current===1,class:_(["btn-gray-sm",{"opacity-50 cursor-not-allowed":r.current===1}])},t[52]||(t[52]=[e("i",{class:"fas fa-chevron-left"},null,-1)]),10,ye),(o(!0),n(J,null,G(dt.value,s=>(o(),n("span",{key:s,class:_(["page-number",{"bg-blue-600 text-white":s===r.current}]),onClick:d=>F(s)},i(s),11,ge))),128)),e("button",{onClick:t[5]||(t[5]=s=>F(r.current+1)),disabled:r.current===r.totalPages,class:_(["btn-gray-sm",{"opacity-50 cursor-not-allowed":r.current===r.totalPages}])},t[53]||(t[53]=[e("i",{class:"fas fa-chevron-right"},null,-1)]),10,be)])])]))]),C.value?(o(),n("div",{key:0,class:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50",onClick:t[11]||(t[11]=St(s=>C.value=!1,["self"]))},[e("div",xe,[e("div",_e,[t[55]||(t[55]=e("h3",{class:"text-xl font-semibold"},"訂單詳情",-1)),e("button",{onClick:t[6]||(t[6]=s=>C.value=!1),class:"text-gray-500 hover:text-gray-700 p-2"},t[54]||(t[54]=[e("i",{class:"fas fa-times text-xl"},null,-1)]))]),a.value?(o(),n("div",he,[e("div",we,[e("div",ke,[t[56]||(t[56]=e("label",{class:"info-label"},"訂單編號",-1)),e("div",Ce,i(a.value.orderId),1)]),e("div",$e,[t[57]||(t[57]=e("label",{class:"info-label"},"訂購日期",-1)),e("div",Ie,i(E(a.value.createdAt)),1)]),e("div",Se,[t[58]||(t[58]=e("label",{class:"info-label"},"客戶姓名",-1)),e("div",De,i(a.value.clientName),1)]),e("div",je,[t[59]||(t[59]=e("label",{class:"info-label"},"訂單狀態",-1)),e("div",Me,[e("span",{class:_(X(a.value.status))},i(W(a.value.status)),3)])]),e("div",Ve,[t[60]||(t[60]=e("label",{class:"info-label"},"付款狀態",-1)),e("div",Ae,[e("span",{class:_(Q(a.value.payment_status))},[m(i(K(a.value.payment_status))+" ",1),a.value.payment_date?(o(),n("span",Pe," ("+i(E(a.value.payment_date))+") ",1)):f("",!0)],2)])]),e("div",ze,[t[61]||(t[61]=e("label",{class:"info-label"},"匯款日期",-1)),e("div",Ne,[a.value.remittance_date?(o(),n("span",Ue,i(Z(a.value.remittance_date)),1)):(o(),n("span",Te,"未設定"))])]),e("div",Ye,[t[62]||(t[62]=e("label",{class:"info-label"},"訂購人",-1)),e("div",Be,i(a.value.orderer||a.value.receiver||"未提供"),1)]),e("div",Oe,[t[63]||(t[63]=e("label",{class:"info-label"},"訂購人電話",-1)),e("div",Re,i(a.value.orderer_phone||a.value.phone||"未提供"),1)]),t[71]||(t[71]=e("div",{class:"info-group"},null,-1)),e("div",qe,[t[64]||(t[64]=e("label",{class:"info-label"},"收貨人",-1)),e("div",Fe,i(a.value.receiver||"未提供"),1)]),e("div",Ee,[t[65]||(t[65]=e("label",{class:"info-label"},"收貨人電話",-1)),e("div",Le,i(a.value.receiver_phone||a.value.phone||"未提供"),1)]),t[72]||(t[72]=e("div",{class:"w-full col-span-2 md:col-span-3"},null,-1)),e("div",Je,[t[66]||(t[66]=e("label",{class:"info-label"},"住址（詳細地址）",-1)),e("div",Ge,i(a.value.detailed_address&&a.value.detailed_address!=="/"?a.value.detailed_address:"未提供"),1)]),e("div",He,[t[67]||(t[67]=e("label",{class:"info-label"},"儲互社",-1)),e("div",Ke,i(a.value.cooperative&&a.value.cooperative!=="/"?a.value.cooperative:"未提供"),1)]),t[73]||(t[73]=e("div",{class:"w-full col-span-2 md:col-span-3"},null,-1)),e("div",Qe,[t[68]||(t[68]=e("label",{class:"info-label"},"抬頭",-1)),e("div",We,i(a.value.invoice_title&&a.value.invoice_title!=="/"?a.value.invoice_title:"未提供"),1)]),e("div",Xe,[t[69]||(t[69]=e("label",{class:"info-label"},"統編",-1)),e("div",Ze,i(a.value.tax_id&&a.value.tax_id!=="/"?a.value.tax_id:"未提供"),1)]),e("div",ts,[t[70]||(t[70]=e("label",{class:"info-label"},"備註",-1)),e("div",es,i(a.value.notes&&a.value.notes!=="/"?a.value.notes:"無"),1)])]),e("div",null,[t[76]||(t[76]=e("h4",{class:"text-lg font-medium mb-2"},"訂購商品",-1)),z.value.some(s=>!s.count&&!s.quantity)?(o(),n("div",ss,t[74]||(t[74]=[e("i",{class:"fas fa-exclamation-triangle text-yellow-600 mt-0.5 mr-2"},null,-1),e("div",{class:"text-sm text-yellow-800"},[e("p",{class:"font-medium"},"數量資訊提示"),e("p",{class:"mt-1"},"由於系統限制，部分商品的數量資訊可能無法正確顯示。如需確認實際購買數量，請參考訂單總金額或聯繫客戶確認。")],-1)]))):f("",!0),e("div",ls,[e("table",as,[t[75]||(t[75]=e("thead",{class:"bg-gray-50"},[e("tr",null,[e("th",{class:"th-sm"},"商品圖片"),e("th",{class:"th-sm"},"商品名稱"),e("th",{class:"th-sm"},"單價"),e("th",{class:"th-sm"},[m(" 數量 "),e("span",{class:"text-xs text-gray-500 font-normal ml-1",title:"數量資訊可能因後端限制而不準確"},[e("i",{class:"fas fa-info-circle"})])]),e("th",{class:"th-sm"},"小計")])],-1)),e("tbody",os,[(o(!0),n(J,null,G(z.value,(s,d)=>(o(),n("tr",{key:d,class:"hover:bg-gray-50"},[e("td",ns,[e("div",is,[e("img",{src:kt(s.image),alt:s.name,class:"w-full h-full object-cover",onError:ht,onLoad:t[7]||(t[7]=st=>console.log("圖片載入成功:",st.target.src))},null,40,rs),s.image?f("",!0):(o(),n("div",ds," 無圖片 "))])]),e("td",us,i(s.name),1),e("td",cs,"NT$ "+i(P(s.price)),1),e("td",vs,[s.count||s.quantity?(o(),n("span",fs,i(s.count||s.quantity),1)):(o(),n("span",ps,"未知"))]),e("td",ms," NT$ "+i(P(wt(s))),1)]))),128))])])])]),e("div",ys,[e("div",gs,[e("div",bs,[t[77]||(t[77]=e("span",null,"商品總計：",-1)),e("span",null,"NT$ "+i(P(a.value.total)),1)]),e("div",xs,[t[78]||(t[78]=e("span",null,"訂單總金額：",-1)),e("span",null,"NT$ "+i(P(a.value.total)),1)])])]),e("div",_s,[e("button",{onClick:t[8]||(t[8]=s=>C.value=!1),class:"btn-gray"},"關閉"),e("button",{onClick:t[9]||(t[9]=s=>tt(a.value)),class:"btn-blue"},t[79]||(t[79]=[e("i",{class:"fas fa-edit mr-2"},null,-1),m(" 更新訂單狀態 ")])),e("button",{onClick:t[10]||(t[10]=s=>et(a.value)),class:"btn-purple"},t[80]||(t[80]=[e("i",{class:"fas fa-money-check-alt mr-2"},null,-1),m(" 更新付款狀態 ")]))])])):f("",!0)])])):f("",!0),D.value?(o(),n("div",hs,[e("div",ws,[e("div",ks,[t[82]||(t[82]=e("h3",{class:"text-xl font-semibold"},"更新付款狀態",-1)),e("button",{onClick:t[12]||(t[12]=s=>D.value=!1),class:"text-gray-500 hover:text-gray-700"},t[81]||(t[81]=[e("i",{class:"fas fa-times"},null,-1)]))]),a.value?(o(),n("div",Cs,[e("div",$s,[t[83]||(t[83]=e("label",{class:"info-label"},"訂單編號",-1)),e("div",Is,i(a.value.orderId),1)]),e("div",Ss,[t[85]||(t[85]=e("label",{for:"paymentStatus",class:"form-label"},"付款狀態",-1)),g(e("select",{id:"paymentStatus","onUpdate:modelValue":t[13]||(t[13]=s=>w.payment_status=s),class:"form-select"},t[84]||(t[84]=[e("option",{value:"1"},"已付款",-1),e("option",{value:"0"},"未付款",-1)]),512),[[Y,w.payment_status]])]),e("div",Ds,[e("button",{onClick:t[14]||(t[14]=s=>D.value=!1),class:"btn-gray"},"取消"),e("button",{onClick:bt,class:"btn-purple",disabled:u.value},[u.value?(o(),n("span",Ms,"處理中...")):(o(),n("span",Vs,t[86]||(t[86]=[e("i",{class:"fas fa-save mr-2"},null,-1),m(" 確認更新")])))],8,js)])])):f("",!0)])])):f("",!0),$.value?(o(),n("div",As,[e("div",Ps,[e("div",zs,[t[88]||(t[88]=e("h3",{class:"text-xl font-semibold"},"更新訂單狀態",-1)),e("button",{onClick:t[15]||(t[15]=s=>$.value=!1),class:"text-gray-500 hover:text-gray-700"},t[87]||(t[87]=[e("i",{class:"fas fa-times"},null,-1)]))]),a.value?(o(),n("div",Ns,[e("div",Us,[t[89]||(t[89]=e("label",{class:"info-label"},"訂單編號",-1)),e("div",Ts,i(a.value.orderId),1)]),e("div",Ys,[t[91]||(t[91]=e("label",{for:"orderStatus",class:"form-label"},"訂單狀態",-1)),g(e("select",{id:"orderStatus","onUpdate:modelValue":t[16]||(t[16]=s=>h.status=s),class:"form-select"},t[90]||(t[90]=[e("option",{value:"1"},"處理中",-1),e("option",{value:"2"},"已出貨",-1),e("option",{value:"3"},"已完成",-1),e("option",{value:"4"},"已取消",-1)]),512),[[Y,h.status]])]),e("div",Bs,[e("button",{onClick:t[17]||(t[17]=s=>$.value=!1),class:"btn-gray"},"取消"),e("button",{onClick:ct,class:"btn-blue",disabled:u.value},[u.value?(o(),n("span",Rs,"處理中...")):(o(),n("span",qs,t[92]||(t[92]=[e("i",{class:"fas fa-save mr-2"},null,-1),m(" 確認更新")])))],8,Os)])])):f("",!0)])])):f("",!0),I.value?(o(),n("div",Fs,[e("div",Es,[e("div",Ls,[t[94]||(t[94]=e("h3",{class:"text-xl font-semibold"},"批量更新訂單狀態",-1)),e("button",{onClick:t[18]||(t[18]=s=>I.value=!1),class:"text-gray-500 hover:text-gray-700"},t[93]||(t[93]=[e("i",{class:"fas fa-times"},null,-1)]))]),e("div",Js,[e("p",Gs,"已選擇 "+i(c.value.length)+" 筆訂單",1),e("div",Hs,[t[96]||(t[96]=e("label",{for:"batchStatus",class:"form-label"},"訂單狀態",-1)),g(e("select",{id:"batchStatus","onUpdate:modelValue":t[19]||(t[19]=s=>N.status=s),class:"form-select"},t[95]||(t[95]=[e("option",{value:"1"},"處理中",-1),e("option",{value:"2"},"已出貨",-1),e("option",{value:"3"},"已完成",-1),e("option",{value:"4"},"已取消",-1)]),512),[[Y,N.status]])]),e("div",Ks,[e("button",{onClick:t[20]||(t[20]=s=>I.value=!1),class:"btn-gray"},"取消"),e("button",{onClick:pt,class:"btn-blue",disabled:u.value},[u.value?(o(),n("span",Ws,"處理中...")):(o(),n("span",Xs,t[97]||(t[97]=[e("i",{class:"fas fa-save mr-2"},null,-1),m(" 確認更新")])))],8,Qs)])])])])):f("",!0),S.value?(o(),n("div",Zs,[e("div",tl,[e("div",el,[t[99]||(t[99]=e("h3",{class:"text-xl font-semibold text-red-600"},"確認刪除",-1)),e("button",{onClick:t[21]||(t[21]=s=>S.value=!1),class:"text-gray-500 hover:text-gray-700"},t[98]||(t[98]=[e("i",{class:"fas fa-times"},null,-1)]))]),e("div",sl,[e("p",ll,"確定要刪除選中的 "+i(c.value.length)+" 筆訂單嗎？",1),t[101]||(t[101]=e("p",{class:"text-sm text-red-500"},"此操作無法復原，請謹慎操作！",-1)),e("div",al,[e("button",{onClick:t[22]||(t[22]=s=>S.value=!1),class:"btn-gray"},"取消"),e("button",{onClick:mt,class:"btn-red",disabled:u.value},[u.value?(o(),n("span",nl,"處理中...")):(o(),n("span",il,t[100]||(t[100]=[e("i",{class:"fas fa-trash-alt mr-2"},null,-1),m(" 確認刪除")])))],8,ol)])])])])):f("",!0),j.value?(o(),n("div",rl,[e("div",dl,[e("div",ul,[t[103]||(t[103]=e("h3",{class:"text-xl font-semibold"},"設定匯款日期",-1)),e("button",{onClick:t[23]||(t[23]=s=>j.value=!1),class:"text-gray-500 hover:text-gray-700"},t[102]||(t[102]=[e("i",{class:"fas fa-times"},null,-1)]))]),e("div",cl,[e("div",vl,[t[104]||(t[104]=e("label",{for:"remittanceDate",class:"form-label"},"匯款日期",-1)),g(e("input",{type:"date",id:"remittanceDate","onUpdate:modelValue":t[24]||(t[24]=s=>b.remittance_date=s),class:"form-input"},null,512),[[T,b.remittance_date]])]),e("div",fl,[e("button",{onClick:t[25]||(t[25]=s=>j.value=!1),class:"btn-gray"},"取消"),e("button",{onClick:xt,class:"btn-blue",disabled:u.value},[u.value?(o(),n("span",ml,"處理中...")):(o(),n("span",yl,t[105]||(t[105]=[e("i",{class:"fas fa-save mr-2"},null,-1),m(" 確認設定")])))],8,pl)])])])])):f("",!0),M.value?(o(),n("div",gl,[e("div",bl,[e("div",xl,[t[107]||(t[107]=e("h3",{class:"text-xl font-semibold"},"批量設定匯款日期",-1)),e("button",{onClick:t[26]||(t[26]=s=>M.value=!1),class:"text-gray-500 hover:text-gray-700"},t[106]||(t[106]=[e("i",{class:"fas fa-times"},null,-1)]))]),e("div",_l,[e("p",hl,"已選擇 "+i(c.value.length)+" 筆訂單",1),e("div",wl,[t[108]||(t[108]=e("label",{for:"batchRemittanceDate",class:"form-label"},"匯款日期",-1)),g(e("input",{type:"date",id:"batchRemittanceDate","onUpdate:modelValue":t[27]||(t[27]=s=>V.remittance_date=s),class:"form-input"},null,512),[[T,V.remittance_date]])]),e("div",kl,[e("button",{onClick:t[28]||(t[28]=s=>M.value=!1),class:"btn-gray"},"取消"),e("button",{onClick:_t,class:"btn-blue",disabled:u.value},[u.value?(o(),n("span",$l,"處理中...")):(o(),n("span",Il,t[109]||(t[109]=[e("i",{class:"fas fa-save mr-2"},null,-1),m(" 確認設定")])))],8,Cl)])])])])):f("",!0)]))}},Ul=Mt(Sl,[["__scopeId","data-v-e50bd5c5"]]);export{Ul as default};
