import{a as n}from"./api-a2dfd2a1.js";import{f as u}from"./vendor-91c90871.js";const{getAdminApiData:i}=n(),a=u(null),t=u([]),o=u(null),f=5*60*1e3;let r=null;const m=async(s=!1)=>{const e=Date.now();if(!s&&a.value&&o.value&&e-o.value<f)return{success:!0,data:a.value,flatCategories:t.value,fromCache:!0};if(r&&!s)return await r;r=g(e);try{return await r}finally{r=null}},g=async s=>{try{const e=await i("admin/categories");if(e&&e.success){const l=e.data||[],c=e.flatCategories||[];if(l.length>0||c.length>0)return a.value=l,t.value=c,o.value=s,{success:!0,data:a.value,flatCategories:t.value,fromCache:!1};if(a.value&&t.value)return console.warn("API 返回空數據，使用快取數據"),{success:!0,data:a.value,flatCategories:t.value,fromCache:!0};throw new Error("分類數據為空")}else throw new Error((e==null?void 0:e.message)||"載入分類失敗")}catch(e){return console.error("❌ 載入分類失敗:",e),a.value&&t.value?(console.warn("載入失敗，使用快取數據"),{success:!0,data:a.value,flatCategories:t.value,fromCache:!0,error:e.message}):{success:!1,message:e.message,data:[],flatCategories:[]}}};export{m as l};
