import{S as A,U as m,V as P,W as R,X as L,Y as O,R as I,Z as T}from"./vendor-91c90871.js";import{m as S,A as b}from"./ant-design-48c6fae4.js";(function(){const n=document.createElement("link").relList;if(n&&n.supports&&n.supports("modulepreload"))return;for(const e of document.querySelectorAll('link[rel="modulepreload"]'))i(e);new MutationObserver(e=>{for(const o of e)if(o.type==="childList")for(const s of o.addedNodes)s.tagName==="LINK"&&s.rel==="modulepreload"&&i(s)}).observe(document,{childList:!0,subtree:!0});function r(e){const o={};return e.integrity&&(o.integrity=e.integrity),e.referrerPolicy&&(o.referrerPolicy=e.referrerPolicy),e.crossOrigin==="use-credentials"?o.credentials="include":e.crossOrigin==="anonymous"?o.credentials="omit":o.credentials="same-origin",o}function i(e){if(e.ep)return;e.ep=!0;const o=r(e);fetch(e.href,o)}})();function k(t){return typeof t=="object"&&t!==null}function _(t,n){return t=k(t)?t:Object.create(null),new Proxy(t,{get(r,i,e){return i==="key"?Reflect.get(r,i,e):Reflect.get(r,i,e)||Reflect.get(n,i,e)}})}function D(t,n){return n.reduce((r,i)=>r==null?void 0:r[i],t)}function V(t,n,r){return n.slice(0,-1).reduce((i,e)=>/^(__proto__)$/.test(e)?{}:i[e]=i[e]||{},t)[n[n.length-1]]=r,t}function $(t,n){return n.reduce((r,i)=>{const e=i.split(".");return V(r,e,D(t,e))},{})}function w(t,n){return r=>{var i;try{const{storage:e=localStorage,beforeRestore:o=void 0,afterRestore:s=void 0,serializer:a={serialize:JSON.stringify,deserialize:JSON.parse},key:d=n.$id,paths:c=null,debug:l=!1}=r;return{storage:e,beforeRestore:o,afterRestore:s,serializer:a,key:((i=t.key)!=null?i:f=>f)(typeof d=="string"?d:d(n.$id)),paths:c,debug:l}}catch(e){return r.debug&&console.error("[pinia-plugin-persistedstate]",e),null}}}function h(t,{storage:n,serializer:r,key:i,debug:e}){try{const o=n==null?void 0:n.getItem(i);o&&t.$patch(r==null?void 0:r.deserialize(o))}catch(o){e&&console.error("[pinia-plugin-persistedstate]",o)}}function g(t,{storage:n,serializer:r,key:i,paths:e,debug:o}){try{const s=Array.isArray(e)?$(t,e):t;n.setItem(i,r.serialize(s))}catch(s){o&&console.error("[pinia-plugin-persistedstate]",s)}}function B(t={}){return n=>{const{auto:r=!1}=t,{options:{persist:i=r},store:e,pinia:o}=n;if(!i)return;if(!(e.$id in o.state.value)){const a=o._s.get(e.$id.replace("__hot:",""));a&&Promise.resolve().then(()=>a.$persist());return}const s=(Array.isArray(i)?i.map(a=>_(a,t)):[_(i,t)]).map(w(t,e)).filter(Boolean);e.$persist=()=>{s.forEach(a=>{g(e.$state,a)})},e.$hydrate=({runHooks:a=!0}={})=>{s.forEach(d=>{const{beforeRestore:c,afterRestore:l}=d;a&&(c==null||c(n)),h(e,d),a&&(l==null||l(n))})},s.forEach(a=>{const{beforeRestore:d,afterRestore:c}=a;d==null||d(n),h(e,a),c==null||c(n),e.$subscribe((l,f)=>{g(f,a)},{detached:!0})})}}var N=B();const z=(t,n)=>{const r=t.__vccOpts||t;for(const[i,e]of n)r[i]=e;return r},C={},K={id:"admin-app"};function q(t,n){const r=A("router-view");return m(),P("div",K,[(m(),R(r,{key:t.$route.fullPath}))])}const U=z(C,[["render",q]]),J="modulepreload",M=function(t){return"/admin/"+t},v={},u=function(n,r,i){if(!r||r.length===0)return n();const e=document.getElementsByTagName("link");return Promise.all(r.map(o=>{if(o=M(o),o in v)return;v[o]=!0;const s=o.endsWith(".css"),a=s?'[rel="stylesheet"]':"";if(!!i)for(let l=e.length-1;l>=0;l--){const f=e[l];if(f.href===o&&(!s||f.rel==="stylesheet"))return}else if(document.querySelector(`link[href="${o}"]${a}`))return;const c=document.createElement("link");if(c.rel=s?"stylesheet":J,s||(c.as="script",c.crossOrigin=""),c.href=o,document.head.appendChild(c),s)return new Promise((l,f)=>{c.addEventListener("load",l),c.addEventListener("error",()=>f(new Error(`Unable to preload CSS for ${o}`)))})})).then(()=>n()).catch(o=>{const s=new Event("vite:preloadError",{cancelable:!0});if(s.payload=o,window.dispatchEvent(s),!s.defaultPrevented)throw o})},W=()=>{const t=localStorage.getItem("adminToken"),n=localStorage.getItem("token"),r=localStorage.getItem("adminLogin");if(r!=="true")return!1;if(r==="true"&&!t&&!n)return!0;const i=t||n;if(!i||i==="null"||i==="undefined")return!1;try{const e=i.split(".");if(e.length!==3)return!1;const o=JSON.parse(atob(e[1]));if(o.exp){const s=Math.floor(Date.now()/1e3);if(o.exp<s)return!1}return!(o.role&&!["admin","superadmin"].includes(o.role))}catch(e){return console.error("管理員 Token 驗證失敗:",e),!1}},j=()=>{["adminLogin","adminToken","adminUser","adminData","adminSession"].forEach(r=>{localStorage.removeItem(r)}),["token","isLogin","user","userData","belongCp"].forEach(r=>{localStorage.removeItem(r)})},E=L({history:O("/admin/"),routes:[{path:"/login",component:()=>u(()=>import("./AdminLoginView-f500bda7.js"),["assets/AdminLoginView-f500bda7.js","assets/vendor-91c90871.js","assets/utils-95cec1c7.js","assets/ant-design-48c6fae4.js","assets/apiConfig-ad0108da.js","assets/AdminLoginView-3df24f7d.css"]),meta:{guest:!0}},{path:"/",component:()=>u(()=>import("./AdminLayout-51d6fd75.js"),["assets/AdminLayout-51d6fd75.js","assets/vendor-91c90871.js","assets/ant-design-48c6fae4.js","assets/AdminLayout-2285a33c.css"]),meta:{requiresAdminAuth:!0},children:[{path:"",redirect:"dashboard"},{path:"dashboard",component:()=>u(()=>import("./DashboardView-e69e67b2.js"),["assets/DashboardView-e69e67b2.js","assets/api-a2dfd2a1.js","assets/utils-95cec1c7.js","assets/ant-design-48c6fae4.js","assets/vendor-91c90871.js","assets/apiConfig-ad0108da.js","assets/DashboardView-e3b0c442.css"])},{path:"products",component:()=>u(()=>import("./ProductsView-bc8b5f4d.js"),["assets/ProductsView-bc8b5f4d.js","assets/vendor-91c90871.js","assets/api-a2dfd2a1.js","assets/utils-95cec1c7.js","assets/ant-design-48c6fae4.js","assets/apiConfig-ad0108da.js","assets/categoryStore-7cbbd32e.js","assets/ProductsView-02202fc6.css"])},{path:"products/add",component:()=>u(()=>import("./ProductFormView-b8d3ceae.js"),["assets/ProductFormView-b8d3ceae.js","assets/vendor-91c90871.js","assets/api-a2dfd2a1.js","assets/utils-95cec1c7.js","assets/ant-design-48c6fae4.js","assets/apiConfig-ad0108da.js","assets/style-5fb4a138.js","assets/style-790ea523.css","assets/categoryStore-7cbbd32e.js","assets/ProductFormView-cf90a419.css"])},{path:"products/edit/:id",component:()=>u(()=>import("./ProductFormView-b8d3ceae.js"),["assets/ProductFormView-b8d3ceae.js","assets/vendor-91c90871.js","assets/api-a2dfd2a1.js","assets/utils-95cec1c7.js","assets/ant-design-48c6fae4.js","assets/apiConfig-ad0108da.js","assets/style-5fb4a138.js","assets/style-790ea523.css","assets/categoryStore-7cbbd32e.js","assets/ProductFormView-cf90a419.css"])},{path:"brands",component:()=>u(()=>import("./BrandsView-39a7bdb8.js"),["assets/BrandsView-39a7bdb8.js","assets/vendor-91c90871.js","assets/api-a2dfd2a1.js","assets/utils-95cec1c7.js","assets/ant-design-48c6fae4.js","assets/apiConfig-ad0108da.js","assets/style-5fb4a138.js","assets/style-790ea523.css","assets/BrandsView-9c69325c.css"])},{path:"categories",component:()=>u(()=>import("./CategoriesView-a65d53a9.js"),["assets/CategoriesView-a65d53a9.js","assets/vendor-91c90871.js","assets/api-a2dfd2a1.js","assets/utils-95cec1c7.js","assets/ant-design-48c6fae4.js","assets/apiConfig-ad0108da.js","assets/CategoriesView-589a33d2.css"])},{path:"users",component:()=>u(()=>import("./UsersView-c23b9273.js"),["assets/UsersView-c23b9273.js","assets/vendor-91c90871.js","assets/api-a2dfd2a1.js","assets/utils-95cec1c7.js","assets/ant-design-48c6fae4.js","assets/apiConfig-ad0108da.js","assets/UsersView-d3e109b5.css"])},{path:"comments",component:()=>u(()=>import("./CommentsView-2775bff5.js"),["assets/CommentsView-2775bff5.js","assets/vendor-91c90871.js","assets/api-a2dfd2a1.js","assets/utils-95cec1c7.js","assets/ant-design-48c6fae4.js","assets/apiConfig-ad0108da.js","assets/CommentsView-19117375.css"])},{path:"orders",component:()=>u(()=>import("./OrdersView-e7bf1ee8.js"),["assets/OrdersView-e7bf1ee8.js","assets/vendor-91c90871.js","assets/api-a2dfd2a1.js","assets/utils-95cec1c7.js","assets/ant-design-48c6fae4.js","assets/apiConfig-ad0108da.js","assets/OrdersView-0d561386.css"])},{path:"settings",component:()=>u(()=>import("./SettingsView-a889e254.js"),["assets/SettingsView-a889e254.js","assets/vendor-91c90871.js","assets/api-a2dfd2a1.js","assets/utils-95cec1c7.js","assets/ant-design-48c6fae4.js","assets/apiConfig-ad0108da.js","assets/style-5fb4a138.js","assets/style-790ea523.css","assets/SettingsView-3eca7a63.css"])},{path:"profile",component:()=>u(()=>import("./AdminProfileView-4f237113.js"),["assets/AdminProfileView-4f237113.js","assets/vendor-91c90871.js","assets/api-a2dfd2a1.js","assets/utils-95cec1c7.js","assets/ant-design-48c6fae4.js","assets/apiConfig-ad0108da.js","assets/AdminProfileView-377adb62.css"])}]},{path:"/:pathMatch(.*)*",redirect:"/"}]});E.beforeEach((t,n,r)=>{if(t.meta.guest)return r();if(t.meta.requiresAdminAuth&&!W())return j(),n.path!=="/login"&&S.warning("管理員登入已過期，請重新登入"),r("/login");r()});const p=I(U),y=T();y.use(N);p.use(y);p.use(E);p.use(b);p.config.globalProperties.$adminMode=!0;p.mount("#app");export{z as _,E as r};
