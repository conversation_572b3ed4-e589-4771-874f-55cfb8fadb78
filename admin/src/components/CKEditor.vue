<template>
  <div class="ckeditor-container">
    <div ref="editorElement" class="editor-element"></div>
  </div>
</template>

<script setup>
import { ref, watch, onMounted, onBeforeUnmount, reactive, nextTick, shallowRef } from 'vue'
import axios from 'axios'

// 定義 props
const props = defineProps({
  modelValue: {
    type: String,
    default: ''
  },
  height: {
    type: Number,
    default: 400
  },
  placeholder: {
    type: String,
    default: '請輸入內容...'
  },
  enableImageUpload: {
    type: Boolean,
    default: true
  },
  enableDelayedUpload: {
    type: Boolean,
    default: false
  },
  hideMediaButtons: {
    type: Boolean,
    default: false
  }
})

// 定義 emits
const emits = defineEmits(['update:modelValue', 'change', 'images-ready'])

// DOM 元素引用
const editorElement = ref(null)

// 使用 shallowRef 避免 Vue 3 的深度響應式問題
const editorInstance = shallowRef(null)
const editorContent = ref(props.modelValue || '')
const isEditorReady = ref(false)
const isUpdating = ref(false)

// 上傳相關狀態
const uploadStates = reactive({
  pendingImages: [],  // 待上傳的圖片
  deletedImages: [],  // 已刪除的圖片
  previousServerImages: [],  // 用於比對的先前服務器圖片
})

// 載入 CKEditor 5 CDN - 使用 Decoupled Document 版本以支援更多插件
const loadCKEditor = () => {
  return new Promise((resolve, reject) => {
    // 檢查是否已經載入
    if (window.DecoupledEditor) {
      resolve()
      return
    }

    // 檢查是否正在載入中（避免重複載入）
    if (window._ckEditorLoading) {
      // 等待載入完成
      const checkInterval = setInterval(() => {
        if (window.DecoupledEditor) {
          clearInterval(checkInterval)
          resolve()
        }
      }, 100)
      return
    }

    // 標記正在載入
    window._ckEditorLoading = true

    // 創建 script 標籤載入 CKEditor 5 - 使用 decoupled-document 版本
    const script = document.createElement('script')
    script.src = 'https://cdn.ckeditor.com/ckeditor5/40.2.0/decoupled-document/ckeditor.js'
    script.async = true
    
    // 載入繁體中文語言包
    const langScript = document.createElement('script')
    langScript.src = 'https://cdn.ckeditor.com/ckeditor5/40.2.0/decoupled-document/translations/zh.js'
    langScript.async = true

    let loadedCount = 0
    const checkLoaded = () => {
      loadedCount++
      if (loadedCount === 2) {
        console.log('CKEditor 5 載入成功')
        window._ckEditorLoading = false
        resolve()
      }
    }

    script.onload = checkLoaded
    script.onerror = () => {
      console.error('CKEditor 5 載入失敗')
      window._ckEditorLoading = false
      reject(new Error('Failed to load CKEditor 5'))
    }
    
    langScript.onload = checkLoaded
    langScript.onerror = () => {
      console.error('CKEditor 5 語言包載入失敗')
      window._ckEditorLoading = false
      reject(new Error('Failed to load CKEditor 5 language pack'))
    }

    document.head.appendChild(script)
    document.head.appendChild(langScript)
  })
}

// 創建上傳適配器類
class MyUploadAdapter {
  constructor(loader, options) {
    this.loader = loader
    this.options = options
  }

  upload() {
    return this.loader.file.then(file => {
      if (this.options.enableDelayedUpload) {
        // 延遲上傳模式
        return new Promise((resolve) => {
          const reader = new FileReader()
          reader.onload = (e) => {
            const base64 = e.target.result
            const tempId = `temp_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`
            
            this.options.uploadStates.pendingImages.push({
              id: tempId,
              file,
              base64
            })
            
            resolve({
              default: base64
            })
            
            this.options.emitImagesState()
          }
          reader.readAsDataURL(file)
        })
      } else {
        // 立即上傳模式
        const formData = new FormData()
        formData.append('file', file)
        
        return axios.post('/api/admin/upload/image', formData, {
          headers: {
            'Content-Type': 'multipart/form-data'
          }
        }).then(response => ({
          default: response.data.data.url
        }))
      }
    })
  }

  abort() {
    // 處理上傳中斷
  }
}

// 初始化編輯器
const initEditor = async () => {
  if (!editorElement.value || !window.DecoupledEditor) {
    return
  }

  try {
    // 創建編輯器配置
    const toolbarItems = [
      'heading', '|',
      'fontFamily', 'fontSize', '|',
      'bold', 'italic', 'underline', 'strikethrough', '|',
      'fontColor', 'fontBackgroundColor', '|',
      'alignment', '|',
      'numberedList', 'bulletedList', '|',
      'outdent', 'indent', '|',
      'link', 'blockQuote', 'insertTable', '|'
    ]
    
    // 根據 props 決定是否顯示圖片和影片按鈕
    if (!props.hideMediaButtons) {
      toolbarItems.push('uploadImage', 'resizeImage', 'mediaEmbed', '|')
    }
    
    toolbarItems.push('undo', 'redo')
    
    const config = {
      language: 'zh',
      placeholder: props.placeholder,
      toolbar: {
        items: toolbarItems
      },
      // 字體家族配置
      fontFamily: {
        options: [
          'default',
          '微軟正黑體, Microsoft JhengHei, sans-serif',
          '新細明體, PMingLiU, serif',
          '標楷體, DFKai-SB, serif',
          'Arial, sans-serif',
          'Times New Roman, serif',
          'Georgia, serif',
          'Verdana, sans-serif',
          'Helvetica, sans-serif',
          'Courier New, monospace',
          'Comic Sans MS, cursive',
          'Impact, sans-serif',
          'Lucida Console, monospace',
          'Tahoma, sans-serif',
          'Trebuchet MS, sans-serif',
          'Palatino, serif'
        ],
        supportAllValues: true
      },
      // 字體大小配置
      fontSize: {
        options: [
          9,
          10,
          11,
          12,
          'default',
          14,
          16,
          18,
          20,
          22,
          24,
          26,
          28,
          32,
          36,
          40,
          48,
          56,
          64,
          72
        ],
        supportAllValues: true
      },
      image: {
        // 圖片工具列配置
        toolbar: [
          'imageTextAlternative',
          'toggleImageCaption',
          '|',
          'imageStyle:inline',
          'imageStyle:alignLeft',
          'imageStyle:alignCenter',
          'imageStyle:alignRight',
          '|',
          'resizeImage'
        ],
        styles: [
          'inline',
          'alignLeft',
          'alignCenter',
          'alignRight'
        ],
        // 圖片縮放配置
        resizeUnit: '%',
        resizeOptions: [
          {
            name: 'resizeImage:original',
            value: null,
            label: '原始大小',
            icon: 'original'
          },
          {
            name: 'resizeImage:25',
            value: '25',
            label: '25%'
          },
          {
            name: 'resizeImage:30',
            value: '30',
            label: '30%'
          },
          {
            name: 'resizeImage:40',
            value: '40',
            label: '40%'
          },
          {
            name: 'resizeImage:50',
            value: '50',
            label: '50%'
          },
          {
            name: 'resizeImage:60',
            value: '60',
            label: '60%'
          },
          {
            name: 'resizeImage:75',
            value: '75',
            label: '75%'
          },
          {
            name: 'resizeImage:100',
            value: '100',
            label: '100%'
          }
        ]
      },
      table: {
        contentToolbar: ['tableColumn', 'tableRow', 'mergeTableCells']
      },
      mediaEmbed: {
        previewsInData: true,
        providers: [
          {
            name: 'youtube',
            url: [
              /^(?:m\.)?youtube\.com\/watch\?v=([\w-]+)/,
              /^(?:m\.)?youtube\.com\/v\/([\w-]+)/,
              /^youtube\.com\/embed\/([\w-]+)/,
              /^youtu\.be\/([\w-]+)/,
              /^(?:m\.)?youtube\.com\/shorts\/([\w-]+)/
            ],
            html: match => {
              const id = match[1]
              return `<div style="position: relative; padding-bottom: 56.25%; height: 0; overflow: hidden;">
                <iframe src="https://www.youtube.com/embed/${id}?rel=0&modestbranding=1" 
                  style="position: absolute; top: 0; left: 0; width: 100%; height: 100%; border: none;" 
                  allowfullscreen allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture">
                </iframe>
              </div>`
            }
          },
          {
            name: 'vimeo',
            url: [
              /^vimeo\.com\/(\d+)/,
              /^vimeo\.com\/video\/(\d+)/,
              /^player\.vimeo\.com\/video\/(\d+)/
            ],
            html: match => {
              const id = match[1]
              return `<div style="position: relative; padding-bottom: 56.25%; height: 0; overflow: hidden;">
                <iframe src="https://player.vimeo.com/video/${id}" 
                  style="position: absolute; top: 0; left: 0; width: 100%; height: 100%; border: none;" 
                  allowfullscreen allow="autoplay; fullscreen; picture-in-picture">
                </iframe>
              </div>`
            }
          },
          {
            name: 'bilibili',
            url: [
              /bilibili\.com\/video\/(BV[a-zA-Z0-9]+)/
            ],
            html: match => {
              const bvid = match[1]
              return `<div style="position: relative; padding-bottom: 56.25%; height: 0; overflow: hidden;">
                <iframe src="https://player.bilibili.com/player.html?bvid=${bvid}&high_quality=1" 
                  style="position: absolute; top: 0; left: 0; width: 100%; height: 100%; border: none;" 
                  allowfullscreen>
                </iframe>
              </div>`
            }
          }
        ]
      }
    }

    // 創建編輯器實例
    const editor = await window.DecoupledEditor.create(editorElement.value, config)
    
    // 將工具列插入到編輯器容器中
    const toolbarContainer = document.createElement('div')
    toolbarContainer.className = 'ck-toolbar-container'
    editorElement.value.parentNode.insertBefore(toolbarContainer, editorElement.value)
    toolbarContainer.appendChild(editor.ui.view.toolbar.element)
    
    // 使用 shallowRef 存儲編輯器實例
    editorInstance.value = Object.freeze(editor)
    isEditorReady.value = true

    // 設置初始內容
    if (props.modelValue) {
      isUpdating.value = true
      editor.setData(props.modelValue)
      isUpdating.value = false
    }

    // 設置編輯器高度
    const editorViewElement = editor.editing.view.document.getRoot()
    editor.editing.view.change(writer => {
      writer.setStyle('min-height', `${props.height}px`, editorViewElement)
      writer.setStyle('max-height', `${props.height}px`, editorViewElement)
    })

    // 監聽內容變化
    editor.model.document.on('change:data', () => {
      if (!isUpdating.value) {
        const content = editor.getData()
        editorContent.value = content
        emits('update:modelValue', content)
        emits('change', content)

        // 延遲處理圖片上傳狀態
        if (props.enableDelayedUpload) {
          analyzeImageUploads(content)
        }
      }
    })

    // 配置圖片上傳
    if (props.enableImageUpload) {
      editor.plugins.get('FileRepository').createUploadAdapter = (loader) => {
        return new MyUploadAdapter(loader, {
          enableDelayedUpload: props.enableDelayedUpload,
          uploadStates: uploadStates,
          emitImagesState: emitImagesState
        })
      }
    }

    // 添加自定義樣式以支援圖片縮放
    addCustomStyles()

  } catch (error) {
    console.error('初始化 CKEditor 失敗:', error)
  }
}

// 添加自定義樣式
const addCustomStyles = () => {
  const style = document.createElement('style')
  style.textContent = `
    /* 字體相關樣式 */
    .ck-content {
      font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", "微軟正黑體", "Microsoft JhengHei", Roboto, "Helvetica Neue", Arial, sans-serif;
    }
    
    /* 修復字體大小下拉選單數字重疊問題 */
    .ck-font-size-dropdown .ck-dropdown__panel .ck-list__item {
      min-width: 5em !important;
      padding: 0.5em 1em !important;
      display: flex !important;
      align-items: center !important;
      justify-content: flex-start !important;
    }
    
    /* 字體大小下拉選單容器樣式 */
    .ck-font-size-dropdown .ck-dropdown__panel {
      min-width: 8em !important;
    }
    
    /* 字體家族下拉選單樣式 */
    .ck.ck-dropdown .ck-dropdown__panel .ck-list {
      max-height: 400px;
      overflow-y: auto;
    }
    
    /* 確保字體大小選項正確顯示 */
    .ck-font-size-dropdown .ck-list .ck-list__item .ck-button__label {
      width: 100% !important;
      text-align: left !important;
    }
    
    /* 確保中文字體正確顯示 */
    .ck-content [style*="微軟正黑體"] {
      font-family: "微軟正黑體", "Microsoft JhengHei", sans-serif !important;
    }
    
    .ck-content [style*="新細明體"] {
      font-family: "新細明體", "PMingLiU", serif !important;
    }
    
    .ck-content [style*="標楷體"] {
      font-family: "標楷體", "DFKai-SB", serif !important;
    }
    
    /* 圖片縮放相關樣式 */
    .ck-content .image {
      display: table;
      clear: both;
      text-align: center;
      margin: 0.9em auto;
      min-width: 50px;
    }
    
    .ck-content .image img {
      display: block;
      margin: 0 auto;
      max-width: 100%;
      min-width: 100%;
      height: auto;
    }
    
    .ck-content .image-inline {
      display: inline-flex;
      max-width: 100%;
      align-items: flex-start;
    }
    
    .ck-content .image-inline img {
      flex-grow: 1;
      flex-shrink: 1;
      max-width: 100%;
      height: auto;
    }
    
    /* 圖片大小調整樣式 */
    .ck-content .image.image_resized {
      max-width: 100%;
      display: block;
      box-sizing: border-box;
    }
    
    .ck-content .image.image_resized img {
      width: 100%;
    }
    
    .ck-content .image.image_resized > figcaption {
      display: block;
    }
    
    /* 圖片對齊樣式 - 重要！確保置中正確顯示 */
    .ck-content .image-style-align-center {
      margin-left: auto !important;
      margin-right: auto !important;
      text-align: center !important;
      display: table !important;
    }
    
    .ck-content .image-style-align-center img {
      margin: 0 auto !important;
      display: block !important;
    }
    
    .ck-content .image-style-align-left {
      float: left;
      margin-right: 1.5em;
      margin-left: 0;
    }
    
    .ck-content .image-style-align-right {
      float: right;
      margin-left: 1.5em;
      margin-right: 0;
    }
    
    /* 確保 figure 元素也能正確置中 */
    .ck-content figure.image {
      display: table;
    }
    
    .ck-content figure.image.image-style-align-center {
      margin-left: auto !important;
      margin-right: auto !important;
    }
    
    .ck-content figure.image img {
      display: block;
      margin: 0 auto;
    }
    
    /* 圖片選中時的樣式 */
    .ck-content .image.ck-widget_selected,
    .ck-content .image-inline.ck-widget_selected {
      outline: 3px solid var(--ck-color-focus-border);
      outline-offset: 2px;
    }
    
    /* 圖片縮放控制柄樣式 */
    .ck-widget.image .ck-widget__resizer {
      display: none;
      position: absolute;
      pointer-events: none;
      left: 0;
      top: 0;
      outline: 1px solid var(--ck-color-resizer);
    }
    
    .ck-widget.image.ck-widget_selected .ck-widget__resizer,
    .ck-widget.image.ck-widget__resizer_active .ck-widget__resizer {
      display: block;
    }
    
    .ck-widget.image .ck-widget__resizer__handle {
      position: absolute;
      pointer-events: all;
      width: var(--ck-resizer-size);
      height: var(--ck-resizer-size);
      background: var(--ck-color-focus-border);
      border: var(--ck-resizer-border-width) solid #fff;
      border-radius: var(--ck-resizer-border-radius);
    }
    
    .ck-widget.image .ck-widget__resizer__handle.ck-widget__resizer__handle-top-left {
      top: var(--ck-resizer-offset);
      left: var(--ck-resizer-offset);
      cursor: nwse-resize;
    }
    
    .ck-widget.image .ck-widget__resizer__handle.ck-widget__resizer__handle-top-right {
      top: var(--ck-resizer-offset);
      right: var(--ck-resizer-offset);
      cursor: nesw-resize;
    }
    
    .ck-widget.image .ck-widget__resizer__handle.ck-widget__resizer__handle-bottom-right {
      bottom: var(--ck-resizer-offset);
      right: var(--ck-resizer-offset);
      cursor: nwse-resize;
    }
    
    .ck-widget.image .ck-widget__resizer__handle.ck-widget__resizer__handle-bottom-left {
      bottom: var(--ck-resizer-offset);
      left: var(--ck-resizer-offset);
      cursor: nesw-resize;
    }
    
    /* CSS 變數定義 */
    :root {
      --ck-resizer-size: 10px;
      --ck-resizer-offset: -5px;
      --ck-resizer-border-width: 1px;
      --ck-resizer-border-radius: 2px;
      --ck-color-resizer: hsl(201, 76%, 63%);
      --ck-color-focus-border: hsl(208, 79%, 51%);
    }
    
    /* 修復工具列按鈕樣式 */
    .ck-toolbar .ck-toolbar__items {
      flex-wrap: wrap !important;
    }
    
    /* 確保工具列有足夠的高度 */
    .ck-toolbar {
      min-height: 42px !important;
      padding: 0.5em !important;
    }
    
    /* 優化編輯器滾動條樣式 */
    .ck-content::-webkit-scrollbar {
      width: 8px;
    }
    
    .ck-content::-webkit-scrollbar-track {
      background: #f1f1f1;
      border-radius: 4px;
    }
    
    .ck-content::-webkit-scrollbar-thumb {
      background: #888;
      border-radius: 4px;
    }
    
    .ck-content::-webkit-scrollbar-thumb:hover {
      background: #555;
    }
  `
  document.head.appendChild(style)
}

// 監聽父組件傳入的 modelValue 的變化
watch(() => props.modelValue, async (newValue) => {
  if (newValue !== editorContent.value && editorInstance.value && isEditorReady.value) {
    isUpdating.value = true
    editorInstance.value.setData(newValue || '')
    await nextTick()
    isUpdating.value = false
  }
})

// 分析內容中的圖片
const analyzeImageUploads = (html) => {
  if (!html || !props.enableDelayedUpload) return
  
  const tempDiv = document.createElement('div')
  tempDiv.innerHTML = html
  
  const imgElements = tempDiv.querySelectorAll('img')
  const currentImageSrcs = []
  
  imgElements.forEach(img => {
    const src = img.getAttribute('src')
    if (src && src.startsWith('data:')) {
      currentImageSrcs.push(src)
    }
  })
  
  const existingServerImages = []
  imgElements.forEach(img => {
    const src = img.getAttribute('src')
    if (src && !src.startsWith('data:')) {
      existingServerImages.push(src)
    }
  })
  
  const deletedServerImages = []
  
  if (uploadStates.previousServerImages) {
    uploadStates.previousServerImages.forEach(prevSrc => {
      if (!existingServerImages.includes(prevSrc)) {
        deletedServerImages.push(prevSrc)
      }
    })
  }
  
  uploadStates.deletedImages = [...deletedServerImages]
  uploadStates.previousServerImages = [...existingServerImages]
  
  emitImagesState()
}

// 通知父組件圖片狀態
const emitImagesState = () => {
  emits('images-ready', {
    pendingImages: [...uploadStates.pendingImages],
    deletedImages: [...uploadStates.deletedImages]
  })
}

// 上傳所有待上傳的圖片
const uploadAllPendingImages = async () => {
  if (!props.enableDelayedUpload || uploadStates.pendingImages.length === 0) {
    return { success: true, errors: [] }
  }
  
  const errors = []
  const uploadPromises = []
  
  for (const pendingImage of uploadStates.pendingImages) {
    const promise = (async () => {
      try {
        const formData = new FormData()
        formData.append('file', pendingImage.file)
        
        const response = await axios.post('/api/admin/upload/image', formData, {
          headers: {
            'Content-Type': 'multipart/form-data'
          }
        })
        
        const url = response.data.data.url
        
        // 替換編輯器中的臨時圖片URL
        if (editorInstance.value && isEditorReady.value) {
          isUpdating.value = true
          const content = editorInstance.value.getData()
          const newContent = content.replace(pendingImage.base64, url)
          editorInstance.value.setData(newContent)
          await nextTick()
          isUpdating.value = false
        }
        
        return { id: pendingImage.id, url }
      } catch (error) {
        console.error(`上傳圖片失敗 (ID: ${pendingImage.id}):`, error)
        errors.push({ id: pendingImage.id, error })
        return null
      }
    })()
    
    uploadPromises.push(promise)
  }
  
  const results = await Promise.all(uploadPromises)
  uploadStates.pendingImages = []
  
  return { 
    success: errors.length === 0,
    errors,
    results: results.filter(Boolean)
  }
}

// 刪除服務器上的圖片
const deleteServerImages = async () => {
  if (uploadStates.deletedImages.length === 0) {
    return { success: true, errors: [] }
  }
  
  const errors = []
  const deletePromises = []
  
  for (const imageUrl of uploadStates.deletedImages) {
    const promise = (async () => {
      try {
        const filename = imageUrl.split('/').pop()
        
        await axios.post('/admin/delete/image', { 
          filename
        })
        
        return { url: imageUrl, success: true }
      } catch (error) {
        console.error(`刪除圖片失敗 (URL: ${imageUrl}):`, error)
        errors.push({ url: imageUrl, error })
        return null
      }
    })()
    
    deletePromises.push(promise)
  }
  
  const results = await Promise.all(deletePromises)
  uploadStates.deletedImages = []
  
  return { 
    success: errors.length === 0,
    errors,
    results: results.filter(Boolean)
  }
}

// 組件掛載時初始化
onMounted(async () => {
  try {
    await loadCKEditor()
    await nextTick()
    await initEditor()
    
    // 添加 passive 事件監聽器優化
    addPassiveEventListeners()
  } catch (error) {
    console.error('載入 CKEditor 失敗:', error)
  }
})

// 添加 passive 事件監聽器以提升性能
const addPassiveEventListeners = () => {
  // 等待編輯器完全初始化
  nextTick(() => {
    // 為編輯器容器添加 passive 滾動監聽
    const editorContainer = editorElement.value?.parentNode
    if (editorContainer) {
      // 移除可能存在的舊監聽器
      editorContainer.removeEventListener('wheel', handleWheel)
      editorContainer.removeEventListener('touchstart', handleTouchStart)
      editorContainer.removeEventListener('touchmove', handleTouchMove)
      
      // 添加新的 passive 監聽器
      editorContainer.addEventListener('wheel', handleWheel, { passive: true })
      editorContainer.addEventListener('touchstart', handleTouchStart, { passive: true })
      editorContainer.addEventListener('touchmove', handleTouchMove, { passive: true })
    }
  })
}

// 處理滾輪事件
const handleWheel = (event) => {
  // 滾輪事件處理邏輯
}

// 處理觸摸開始事件
const handleTouchStart = (event) => {
  // 觸摸開始事件處理邏輯
}

// 處理觸摸移動事件
const handleTouchMove = (event) => {
  // 觸摸移動事件處理邏輯
}

// 組件卸載前清理
onBeforeUnmount(() => {
  // 清理事件監聽器
  const editorContainer = editorElement.value?.parentNode
  if (editorContainer) {
    editorContainer.removeEventListener('wheel', handleWheel)
    editorContainer.removeEventListener('touchstart', handleTouchStart)
    editorContainer.removeEventListener('touchmove', handleTouchMove)
  }
  
  if (editorInstance.value && editorInstance.value.destroy) {
    try {
      // 移除工具列容器
      const toolbarContainer = editorElement.value?.parentNode?.querySelector('.ck-toolbar-container')
      if (toolbarContainer) {
        toolbarContainer.remove()
      }
      
      editorInstance.value.destroy()
      editorInstance.value = null
      isEditorReady.value = false
    } catch (error) {
      console.error('銷毀編輯器時出錯:', error)
    }
  }
})

// 暴露方法給父組件
defineExpose({
  editor: editorInstance,
  uploadAllPendingImages,
  deleteServerImages,
  getContent: () => editorContent.value,
  setContent: (html) => {
    editorContent.value = html
    if (editorInstance.value && isEditorReady.value) {
      isUpdating.value = true
      editorInstance.value.setData(html)
      isUpdating.value = false
    }
  }
})
</script>

<style scoped>
.ckeditor-container {
  border: 1px solid #ccc;
  border-radius: 4px;
  overflow: hidden;
  position: relative;
}

.editor-element {
  width: 100%;
}

/* 工具列容器樣式 */
:deep(.ck-toolbar-container) {
  border-bottom: 1px solid #ccc;
}

/* CKEditor 5 樣式覆蓋 */
:deep(.ck-editor__main) {
  min-height: v-bind('props.height + "px"');
}

:deep(.ck-content) {
  min-height: v-bind('props.height + "px"');
  max-height: v-bind('props.height + "px"');
  overflow-y: auto;
}

:deep(.ck-editor__editable) {
  min-height: v-bind('props.height + "px"');
  max-height: v-bind('props.height + "px"');
  border: none;
  border-radius: 0;
}

/* 響應式影片容器 */
:deep(.ck-media__wrapper) {
  position: relative;
  padding-bottom: 56.25%;
  height: 0;
  overflow: hidden;
}

:deep(.ck-media__wrapper iframe) {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  border: none;
}

/* 工具列樣式 */
:deep(.ck-toolbar) {
  border: none !important;
  background-color: #f8f9fa;
}

/* 編輯器內容區域 */
:deep(.ck-content) {
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", "微軟正黑體", "Microsoft JhengHei", Roboto, "Helvetica Neue", Arial, sans-serif;
  font-size: 16px;
  line-height: 1.8;
  padding: 24px;
}

/* 字體選擇器樣式優化 - 修復數字重疊問題 */
:deep(.ck-font-size-dropdown .ck-dropdown__panel .ck-list__item) {
  min-height: 32px !important;
  padding: 0.5em 1em !important;
  display: flex !important;
  align-items: center !important;
}

/* 確保字體大小數字不重疊 */
:deep(.ck-font-size-dropdown .ck-list .ck-list__item .ck-button) {
  width: 100% !important;
  justify-content: flex-start !important;
}

:deep(.ck-font-size-dropdown .ck-list .ck-list__item .ck-button__label) {
  font-size: 14px !important;
  line-height: 1.4 !important;
}

:deep(.ck-font-family-dropdown .ck-dropdown__panel .ck-list__item) {
  font-size: 14px;
  min-height: unset;
  padding: 0.5em 1em;
}

/* 確保字體選擇器中的字體正確顯示 */
:deep(.ck-font-family-dropdown .ck-list .ck-list__item[data-value*="微軟正黑體"]) {
  font-family: "微軟正黑體", "Microsoft JhengHei", sans-serif !important;
}

:deep(.ck-font-family-dropdown .ck-list .ck-list__item[data-value*="新細明體"]) {
  font-family: "新細明體", "PMingLiU", serif !important;
}

:deep(.ck-font-family-dropdown .ck-list .ck-list__item[data-value*="標楷體"]) {
  font-family: "標楷體", "DFKai-SB", serif !important;
}

:deep(.ck-font-family-dropdown .ck-list .ck-list__item[data-value*="Arial"]) {
  font-family: Arial, sans-serif !important;
}

:deep(.ck-font-family-dropdown .ck-list .ck-list__item[data-value*="Times New Roman"]) {
  font-family: "Times New Roman", serif !important;
}

:deep(.ck-font-family-dropdown .ck-list .ck-list__item[data-value*="Georgia"]) {
  font-family: Georgia, serif !important;
}

:deep(.ck-font-family-dropdown .ck-list .ck-list__item[data-value*="Verdana"]) {
  font-family: Verdana, sans-serif !important;
}

:deep(.ck-font-family-dropdown .ck-list .ck-list__item[data-value*="Courier New"]) {
  font-family: "Courier New", monospace !important;
}

:deep(.ck-font-family-dropdown .ck-list .ck-list__item[data-value*="Comic Sans MS"]) {
  font-family: "Comic Sans MS", cursive !important;
}

/* 表格樣式 */
:deep(.ck-content table) {
  border-collapse: collapse;
  width: 100%;
}

:deep(.ck-content table td,
.ck-content table th) {
  border: 1px solid #ddd;
  padding: 8px;
}

/* 引用區塊樣式 */
:deep(.ck-content blockquote) {
  border-left: 4px solid #ccc;
  margin-left: 0;
  padding-left: 16px;
  font-style: italic;
}

/* 程式碼區塊樣式 */
:deep(.ck-content pre) {
  background-color: #f4f4f4;
  border: 1px solid #ddd;
  border-radius: 4px;
  padding: 12px;
  overflow-x: auto;
}

/* 連結樣式 */
:deep(.ck-content a) {
  color: #0066cc;
  text-decoration: underline;
}

:deep(.ck-content a:hover) {
  color: #0052a3;
}

/* 圖片標題樣式 */
:deep(.ck-content .image > figcaption) {
  display: table-caption;
  caption-side: bottom;
  word-break: break-word;
  color: #666;
  background-color: #f7f7f7;
  padding: .6em;
  font-size: .75em;
  outline-offset: -1px;
}

/* 確保圖片縮放控制柄可見 */
:deep(.ck-widget__resizer) {
  z-index: 10;
}

:deep(.ck-widget__resizer__handle) {
  z-index: 11;
}
</style>