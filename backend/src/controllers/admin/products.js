const { executeQuery, executeQuerySingle, executeNonQuery } = require('../../database/db')

// 獲取商品列表 - 優化版本
const getProducts = async (req, res) => {
  try {
    const page = parseInt(req.query.page) || 1
    const pageSize = parseInt(req.query.pageSize) || 10
    const offset = (page - 1) * pageSize
    const keyWord = req.query.keyWord || ''
    const categoryId = req.query.categoryId || ''
    const statusFilter = req.query.status || 'active' // 新增狀態篩選，預設為啟用

    // 減少不必要的分類數據查詢
    let targetCategoryIds = []
    
    // 構建基本查詢條件
    let whereConditions = ['p.isDelete = 0']
    let params = []

    // 狀態篩選
    if (statusFilter === 'active') {
      whereConditions.push('p.is_active = 1')
    } else if (statusFilter === 'inactive') {
      whereConditions.push('p.is_active = 0')
    }
    // 如果是 'all'，則不新增任何條件，顯示所有狀態的商品

    // 關鍵字搜索
    if (keyWord) {
      whereConditions.push('p.name LIKE ?')
      params.push(`%${keyWord}%`)
    }

    // 簡化分類過濾邏輯
    let joinClause = 'LEFT JOIN product_categories pc ON p.id = pc.product_id'
    
    if (categoryId && categoryId !== '') {
      const targetCategoryId = parseInt(categoryId)
      if (!isNaN(targetCategoryId)) {
        // 只有在需要時才查詢分類數據
        const categoriesData = await executeQuery('SELECT id, parent_id FROM categories')
        targetCategoryIds = [targetCategoryId]
        
        // 遞歸找出所有子分類
        const findChildCategories = (parentId) => {
          const children = categoriesData.filter(cat => cat.parent_id === parentId)
          children.forEach(child => {
            targetCategoryIds.push(child.id)
            findChildCategories(child.id)
          })
        }
        
        findChildCategories(targetCategoryId)
        
        const placeholders = targetCategoryIds.map(() => '?').join(',')
        whereConditions.push(`pc.category_id IN (${placeholders})`)
        params.push(...targetCategoryIds)
        
        // 當有分類篩選時使用INNER JOIN
        joinClause = 'INNER JOIN product_categories pc ON p.id = pc.product_id'
      }
    }

    const whereClause = whereConditions.join(' AND ')

    // 使用更簡單的總數查詢
    const countQuery = `
      SELECT COUNT(DISTINCT p.id) as total 
      FROM products p 
      ${joinClause}
      WHERE ${whereClause}
    `
    const countResult = await executeQuerySingle(countQuery, params)
    const total = countResult ? countResult.total : 0

    // 簡化主查詢，移除複雜的子查詢
    const query = `
      SELECT DISTINCT p.*,
        pc.category_id as primary_category_id
      FROM products p 
      ${joinClause}
      WHERE ${whereClause}
      GROUP BY p.id 
      ORDER BY p.createdAt DESC 
      LIMIT ? OFFSET ?
    `
    
    const queryParams = [...params, pageSize, offset]
    const products = await executeQuery(query, queryParams)

    // 批量獲取分類信息而不是為每個商品單獨查詢
    if (products.length > 0) {
      const productIds = products.map(p => p.id)
      const placeholders = productIds.map(() => '?').join(',')
      
      const categoriesQuery = `
        SELECT pc.product_id, c.name, c.id,
          GROUP_CONCAT(
            CASE WHEN c.parent_id IS NULL THEN c.name 
                 ELSE (SELECT parent.name FROM categories parent WHERE parent.id = c.parent_id) || ' > ' || c.name 
            END
          ) as category_path
        FROM product_categories pc
        JOIN categories c ON pc.category_id = c.id
        WHERE pc.product_id IN (${placeholders}) AND pc.is_primary = 1
        GROUP BY pc.product_id
      `
      
      const categoriesData = await executeQuery(categoriesQuery, productIds)
      const categoryMap = {}
      categoriesData.forEach(cat => {
        categoryMap[cat.product_id] = cat.category_path
      })
      
      // 將分類信息添加到每個商品
      products.forEach(product => {
        product.category_path = categoryMap[product.id] || product.sort || '未分類'
      })
    }

    res.status(200).json({
      success: true,
      products,
      total,
      page,
      pageSize,
    })
  } catch (error) {
    console.error('獲取商品列表時發生錯誤:', error)
    res.status(500).json({ success: false, message: '獲取商品列表失敗，請稍後再試' })
  }
}

// 獲取單個商品詳情
const getProductById = async (req, res) => {
  try {
    const { id } = req.params

    if (!id) {
      return res.status(400).json({ success: false, message: '商品ID為必填項' })
    }

    // 獲取商品詳情
    const product = await executeQuerySingle(
      'SELECT * FROM products WHERE id = ? AND isDelete = 0',
      [id],
    )

    if (!product) {
      return res.status(404).json({ success: false, message: '找不到商品' })
    }

    // 獲取商品的分類信息
    const categories = await executeQuery(
      `SELECT c.* FROM product_categories pc
       JOIN categories c ON pc.category_id = c.id
       WHERE pc.product_id = ?
       ORDER BY pc.is_primary DESC`,
      [id]
    )

    // 將分類信息添加到商品對象
    product.categories = categories
    
    // 為前端編輯頁面提供 sort 字段（主分類的完整路徑）
    if (categories.length > 0) {
      // 獲取主分類（is_primary = 1，在ORDER BY中已排序到第一位）
      const primaryCategory = categories[0]
      const allCategories = await executeQuery('SELECT * FROM categories')
      product.sort = buildCategoryPath(primaryCategory.id, allCategories)
    } else {
      product.sort = ''
    }

    res.status(200).json({
      success: true,
      product,
    })
  } catch (error) {
    console.error('獲取商品詳情時發生錯誤:', error)
    res.status(500).json({ success: false, message: '獲取商品詳情失敗，請稍後再試' })
  }
}

// 更新商品
const updateProduct = async (req, res) => {
  try {
    const id = req.body.id || req.query.id

    if (!id) {
      return res.status(400).json({ success: false, message: '未提供商品ID' })
    }

    // 檢查商品是否存在
    const product = await executeQuerySingle(
      'SELECT * FROM products WHERE id = ? AND isDelete = 0',
      [id],
    )
    if (!product) {
      return res.status(404).json({ success: false, message: '商品不存在或已被刪除' })
    }

    const updates = { ...req.body }
    delete updates.id
    delete updates.isDelete
    delete updates.createdAt
    delete updates.updatedAt

    // 處理分類ID - 從 categoryId 提取並移除該欄位
    let categoryId = updates.categoryId
    delete updates.categoryId

    // 處理圖片數據
    if (updates.images) {
      let imagesJson
      if (typeof updates.images === 'string') {
        try {
          JSON.parse(updates.images)
          imagesJson = updates.images
        } catch {
          imagesJson = JSON.stringify([updates.images])
        }
      } else if (Array.isArray(updates.images)) {
        imagesJson = JSON.stringify(updates.images)
      } else {
        imagesJson = JSON.stringify([])
      }
      updates.images = imagesJson
    }

    // 處理規格數據
    if (updates.specTypes) {
      updates.specTypes = typeof updates.specTypes === 'string' 
        ? updates.specTypes 
        : JSON.stringify(updates.specTypes)
    }
    
    if (updates.specCombinations) {
      updates.specCombinations = typeof updates.specCombinations === 'string' 
        ? updates.specCombinations 
        : JSON.stringify(updates.specCombinations)
    }

    // 處理重點商品資訊
    if (updates.highlights !== undefined) {
      // 如果是空字串或 null，保持原樣；否則確保是字串
      updates.highlights = updates.highlights || null
    }

    // 如果沒有從前端獲取到 categoryId，嘗試從 sort 欄位解析
    if (updates.sort && updates.sort.trim() !== '') {
      const categoryPath = updates.sort.trim()

      // 查找匹配的分類
      const categories = await executeQuery('SELECT id, name, parent_id FROM categories')

      // 先嘗試完全匹配
      let matchedCategory = categories.find(cat => cat.name === categoryPath)

      if (!matchedCategory && categoryPath.includes(' > ')) {
        // 如果包含 ' > '，嘗試匹配最後一級分類
        const pathParts = categoryPath.split(' > ')
        const lastPart = pathParts[pathParts.length - 1].trim()
        matchedCategory = categories.find(cat => cat.name === lastPart)
      }

      if (matchedCategory && !categoryId) {
        categoryId = matchedCategory.id
      }
    }
    
    // 移除 sort 字段，因為它不是數據庫字段
    delete updates.sort

    // 構建更新語句
    let setClause = []
    let params = []

    for (const [key, value] of Object.entries(updates)) {
      setClause.push(`${key} = ?`)
      params.push(value)
    }

    if (setClause.length === 0 && !categoryId) {
      return res.status(200).json({ success: true, message: '未檢測到更新內容' })
    }

    params.push(id)
    
    // 開始事務
    const db = require('../../database/db').getDb()
    const transaction = db.transaction((data) => {
      try {
        // 更新商品基本信息
        if (data.setClause.length > 0) {
          const updateSql = `UPDATE products SET ${data.setClause.join(', ')}, updatedAt = CURRENT_TIMESTAMP WHERE id = ?`
          db.prepare(updateSql).run(data.params)
        }
        
        // 處理分類關聯
        if (data.categoryId) {
          // 完全替換分類關聯：先刪除所有現有分類關聯
          const deleteStmt = db.prepare(
            'DELETE FROM product_categories WHERE product_id = ?'
          )
          deleteStmt.run(data.productId)
          
          // 添加新的唯一主分類關聯
          const insertStmt = db.prepare(
            'INSERT INTO product_categories (product_id, category_id, is_primary) VALUES (?, ?, 1)'
          )
          insertStmt.run(data.productId, data.categoryId)
        }
        
        return { success: true }
      } catch (error) {
        return { success: false, error: error.message }
      }
    })
    
    const transactionResult = transaction({
      setClause,
      params,
      categoryId: categoryId,
      productId: id
    })
    
    if (!transactionResult.success) {
      throw new Error(transactionResult.error || '更新商品失敗')
    }
    
    res.status(200).json({
      success: true,
      message: '商品更新成功',
    })
  } catch (error) {
    console.error('更新商品時發生錯誤:', error)
    res.status(500).json({ success: false, message: '更新商品失敗，請稍後再試' })
  }
}

// 刪除商品
const deleteProduct = async (req, res) => {
  try {
    const id = req.body.id || req.query.id || req.params.id

    if (!id) {
      return res.status(400).json({ success: false, message: '未提供商品ID' })
    }

    // 檢查商品是否存在
    const product = await executeQuerySingle(
      'SELECT * FROM products WHERE id = ? AND isDelete = 0',
      [id],
    )
    if (!product) {
      return res.status(404).json({ success: false, message: '商品不存在或已被刪除' })
    }

    // 軟刪除商品
    await executeNonQuery('UPDATE products SET isDelete = 1, updatedAt = ? WHERE id = ?', [
      new Date().toISOString(),
      id,
    ])

    res.status(200).json({
      success: true,
      message: '商品刪除成功',
    })
  } catch (error) {
    console.error('刪除商品時發生錯誤:', error)
    res.status(500).json({ success: false, message: '刪除商品失敗，請稍後再試' })
  }
}

// 新增功能：切換商品特色狀態
const toggleFeatured = async (req, res) => {
  try {
    const { id, is_featured } = req.body

    if (!id || is_featured === undefined) {
      return res.status(400).json({ success: false, message: '商品ID和特色狀態為必填項' })
    }

    // 檢查商品是否存在
    const product = await executeQuerySingle(
      'SELECT * FROM products WHERE id = ? AND isDelete = 0',
      [id],
    )
    if (!product) {
      return res.status(404).json({ success: false, message: '商品不存在或已被刪除' })
    }

    // 更新特色狀態
    await executeNonQuery('UPDATE products SET is_featured = ?, updatedAt = ? WHERE id = ?', [
      is_featured ? 1 : 0,
      new Date().toISOString(),
      id,
    ])

    res.status(200).json({
      success: true,
      message: '商品特色狀態更新成功',
    })
  } catch (error) {
    console.error('更新商品特色狀態時發生錯誤:', error)
    res.status(500).json({ success: false, message: '更新商品特色狀態失敗，請稍後再試' })
  }
}

// 新增功能：切換商品啟用/停用狀態
const toggleActive = async (req, res) => {
  try {
    const { id, is_active } = req.body

    if (!id || is_active === undefined) {
      return res.status(400).json({ success: false, message: '商品ID和啟用狀態為必填項' })
    }

    // 檢查商品是否存在
    const product = await executeQuerySingle(
      'SELECT * FROM products WHERE id = ? AND isDelete = 0',
      [id],
    )
    if (!product) {
      return res.status(404).json({ success: false, message: '商品不存在或已被刪除' })
    }

    // 更新啟用狀態
    await executeNonQuery('UPDATE products SET is_active = ?, updatedAt = ? WHERE id = ?', [
      is_active ? 1 : 0,
      new Date().toISOString(),
      id,
    ])

    res.status(200).json({
      success: true,
      message: is_active ? '商品已啟用' : '商品已停用',
    })
  } catch (error) {
    console.error('更新商品啟用狀態時發生錯誤:', error)
    res.status(500).json({ success: false, message: '更新商品啟用狀態失敗，請稍後再試' })
  }
}

// 工具函數：構建分類路徑
const buildCategoryPath = (categoryId, allCategories) => {
  const category = allCategories.find(cat => cat.id === categoryId)
  if (!category) return ''
  
  if (category.parent_id) {
    const parentPath = buildCategoryPath(category.parent_id, allCategories)
    return parentPath ? `${parentPath} > ${category.name}` : category.name
  }
  
  return category.name
}

module.exports = {
  getProducts,
  getProductById,
  updateProduct,
  deleteProduct,
  toggleFeatured,
  toggleActive
}