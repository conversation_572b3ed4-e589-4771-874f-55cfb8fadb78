import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'
import path from 'path'

// 控制前後端連接的變數
const useLocalBackend = false  // 修改為 false，使用遠端後端
const localBackendUrl = 'http://localhost:3003'  // 修改為正確的本機後端端口
const remoteBackendUrl = 'http://************:81'  // 設定為新的遠端後端網址和端口
const backendUrl = useLocalBackend ? localBackendUrl : remoteBackendUrl

// 輸出配置信息
// console.log('Vite 配置信息:')
// console.log(`使用${useLocalBackend ? '本機' : '遠端'}後端: ${backendUrl}`)

// https://vitejs.dev/config/
export default defineConfig({
  plugins: [vue()],
  root: path.resolve(__dirname, 'admin'),  // 設定根目錄為 admin 目錄
  resolve: {
    alias: {
      '@': path.resolve(__dirname, './admin/src'),
    },
  },
  // 優化依賴配置
  optimizeDeps: {
    // 強制優化這些依賴
    include: [
      'vue',
      'vue-router',
      'pinia',
      'axios',
      'dayjs',
      'dayjs/locale/zh-tw',
      'ant-design-vue',
      'lodash'
    ],
    // 強制重新優化依賴
    force: true,
    // 增加依賴掃描深度
    entries: [
      './admin/src/**/*.vue',
      './admin/src/**/*.js'
    ],
    // 增加依賴掃描超時時間
    timeout: 10000
  },
  css: {
    // 啟用 CSS 模組
    modules: {
      localsConvention: 'camelCaseOnly',
    },
    // 啟用 CSS 預處理器
    preprocessorOptions: {
      less: {
        javascriptEnabled: true,
      },
    },
  },
  server: {
    port: 5111,  // 管理後台使用 5111 端口
    host: '0.0.0.0', // 允許外部訪問
    proxy: {
      '/api': {
        target: backendUrl,
        changeOrigin: true,
        secure: false,
        ws: true,
        configure: (proxy, options) => {
          proxy.on('error', (err, req, res) => {
            console.log('代理錯誤:', err);
          });
          proxy.on('proxyReq', (proxyReq, req, res) => {
            console.log('代理請求:', req.method, req.url, '-> ', options.target + req.url);
          });
          proxy.on('proxyRes', (proxyRes, req, res) => {
            console.log('代理響應:', proxyRes.statusCode, req.url);
          });
        }
      },
      '/uploads': {
        target: backendUrl,
        changeOrigin: true,
        secure: false,
        configure: (proxy, options) => {
          proxy.on('error', (err, req, res) => {
            console.log('上傳代理錯誤:', err);
          });
        }
      }
    }
  },
  // 設定後台的基礎路徑為絕對路徑
  base: '/admin/',

  // 增加構建性能配置
  build: {
    // 增加構建緩存
    cssCodeSplit: true,
    sourcemap: false,
    // 減少構建警告
    chunkSizeWarningLimit: 2000,
    // 優化大型依賴
    rollupOptions: {
      output: {
        manualChunks: {
          'vendor': ['vue', 'vue-router', 'pinia'],
          'ant-design': ['ant-design-vue'],
          'utils': ['axios', 'dayjs', 'lodash']
        }
      }
    }
  },

  // 增加開發伺服器配置
  cacheDir: path.resolve(__dirname, 'node_modules/.vite_admin_cache'),
  clearScreen: false,
  logLevel: 'info'
})