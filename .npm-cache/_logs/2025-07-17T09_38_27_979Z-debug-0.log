0 verbose cli /usr/local/bin/node /usr/local/bin/npm
1 info using npm@10.9.2
2 info using node@v22.17.0
3 silly config load:file:/usr/local/lib/node_modules/npm/npmrc
4 silly config load:file:/Users/<USER>/Desktop/culroc---main-main/.npmrc
5 silly config load:file:/Users/<USER>/.npmrc
6 silly config load:file:/usr/local/etc/npmrc
7 verbose title npm run dev
8 verbose argv "run" "dev"
9 verbose logfile logs-max:10 dir:/Users/<USER>/Desktop/culroc---main-main/.npm-cache/_logs/2025-07-17T09_38_27_979Z-
10 verbose logfile /Users/<USER>/Desktop/culroc---main-main/.npm-cache/_logs/2025-07-17T09_38_27_979Z-debug-0.log
11 silly logfile start cleaning logs, removing 1 files
12 silly logfile done cleaning log files
13 verbose stack Error: Missing script: "dev"
13 verbose stack
13 verbose stack To see a list of scripts, run:
13 verbose stack   npm run
13 verbose stack     at #run (/usr/local/lib/node_modules/npm/lib/commands/run-script.js:111:13)
13 verbose stack     at async RunScript.exec (/usr/local/lib/node_modules/npm/lib/commands/run-script.js:40:7)
13 verbose stack     at async Npm.exec (/usr/local/lib/node_modules/npm/lib/npm.js:207:9)
13 verbose stack     at async module.exports (/usr/local/lib/node_modules/npm/lib/cli/entry.js:74:5)
14 error Missing script: "dev"
14 error
14 error To see a list of scripts, run:
14 error   npm run
15 verbose cwd /Users/<USER>/Desktop/culroc---main-main
16 verbose os Darwin 24.5.0
17 verbose node v22.17.0
18 verbose npm  v10.9.2
19 verbose exit 1
20 verbose code 1
21 error A complete log of this run can be found in: /Users/<USER>/Desktop/culroc---main-main/.npm-cache/_logs/2025-07-17T09_38_27_979Z-debug-0.log
