<script setup>
import { RouterLink } from 'vue-router'
import { ref, onMounted, onBeforeUnmount, computed, watch } from 'vue' // 移除 reactive, 添加 computed, watch

import CartItem from './CartItem.vue'
import router from '@/router'
import { storeToRefs } from 'pinia'
import piniaStores from '@/stores/apiStore'
import api from '@/utils/api.js'
import { message } from 'ant-design-vue'
import _ from 'lodash'
const { getApiData, checkAuthToken } = api() // 添加 checkAuthToken

const handelPinia = piniaStores()
const { classSet, carts, isUser, clientData } = storeToRefs(handelPinia) // 添加isUser和clientData
// const scrollY = ref(0); // 移除未使用的 scrollY

// const updateScrollY = () => { // 移除未使用的 updateScrollY
//   scrollY.value = window.scrollY;
// };
// const classSet = ref([])

// 管理展開的節點（移動端樹組件使用）
const expandedKeys = ref([])

// 處理樹節點展開事件
const onExpand = (expandedKeys) => {
  expandedKeys.value = expandedKeys
}

// 添加品牌數據
const brands = ref([])

// 檢查用戶登入狀態
const checkLoginStatus = () => {
  // 使用 API 工具中的 checkAuthToken 函數檢查 token
  const hasToken = checkAuthToken()
  const isLoginFlag = localStorage.getItem('isLogin')

  // 如果有 token 但 isUser 為 false，更新 isUser
  if (hasToken && !isUser.value) {
    isUser.value = true
    // 嘗試獲取用戶資料
    handelPinia.getUserData()
  }
  // 如果沒有 token 但 isUser 為 true，更新 isUser
  else if (!hasToken && isUser.value) {
    isUser.value = false
    clientData.value = {}
  }
}

// 監聽路由變化，每次路由變化時檢查登入狀態
watch(() => router.currentRoute.value, () => {
  checkLoginStatus()
}, { immediate: true })

onMounted(async () => {
  try {
    // 檢查登入狀態
    checkLoginStatus()

    // 首先從 Pinia store 獲取分類數據
    await handelPinia.getClassSet()

    // 如果 classSet 為空，顯示警告
    if (!classSet.value || classSet.value.length === 0) {
      // console.warn('HeaderItem: classSet 數據為空，可能需要重新加載')
    } else {
      // console.log('HeaderItem: 成功獲取 classSet 數據，共', classSet.value.length, '項')
      
      // 調試：輸出完整的分類數據結構
      // console.log('HeaderItem: 完整分類數據結構:', JSON.stringify(classSet.value, null, 2))
      
      // 特別檢查商品館和品牌館的結構
      // const productCat = classSet.value.find((cat) => cat.label === '商品館')
      // const brandCat = classSet.value.find((cat) => cat.label === '品牌館')
      
      // if (productCat) {
      //   console.log('HeaderItem: 商品館分類結構:', JSON.stringify(productCat, null, 2))
      //   console.log('HeaderItem: 商品館子分類數量:', productCat.children?.length || 0)
      // }
      
      // if (brandCat) {
      //   console.log('HeaderItem: 品牌館分類結構:', JSON.stringify(brandCat, null, 2))
      // }
      
      // if (brandCat) {
      //   console.log('HeaderItem: 品牌館分類結構:', JSON.stringify(brandCat, null, 2))
      //   console.log('HeaderItem: 品牌館子分類數量:', brandCat.children?.length || 0)
      // }
    }

    // 獲取品牌資料
    const brandsRes = await getApiData('brands')
    if (brandsRes.resData && brandsRes.resData.length > 0) {
      brands.value = brandsRes.resData
    } else {
      console.warn('HeaderItem: 無法獲取品牌數據')
    }
  } catch (error) {
    console.error('HeaderItem: 獲取數據時發生錯誤:', error)
  }
})
// const isUser = ref()

onBeforeUnmount(() => {
  // window.removeEventListener('scroll', updateScrollY);
})
// defineProps({
//   msg: {
//     type: String,
//     required: true,
//   },
// })

const menuOpen = ref(false)
const cartsOpen = ref(false)

const openMenu = () => {
  menuOpen.value = !menuOpen.value
}

// const handelCarts = (e) => { // 移除未使用的 handelCarts
//   if (e == 'close') {
//     cartsOpen.value = false
//   } else {
//     cartsOpen.value = !cartsOpen.value;
//
//   }
// }

// const setClass = [ // 移除未使用的 setClass
//   {
//     "value": "iPhone ",
// ... (移除整個 setClass 陣列)
//   }
// ]

const menuList = ref([])
const showChildren = ref(false)

const cartDiv = ref()

const goPay = () => {
  cartsOpen.value = false
  router.push('/check')
  // if (isUser.value) {
  //   router.push('/check')
  // } else {
  //   router.push('login')

  // }
}

// const searchData = reactive({ keyWord: '', sort: '' }) // 移除未使用的 searchData
const searchName = ref('')

const searchProducts = () => {
  if (!searchName.value.trim()) return; // 如果搜索字串為空，不執行跳轉

  if (router.currentRoute.value.path == '/products') {
    // console.log('相同')
    router.replace({
      path: '/products',
      query: { keyWord: searchName.value },
    })
  } else {
    // console.log('不同')

    router.push({
      path: '/products',
      query: { keyWord: searchName.value },
    })
  }
  searchName.value = ''
}

const logout = () => {
  // 清除登入狀態
  localStorage.removeItem('isLogin')
  localStorage.removeItem('token')

  // 清除記住我的相關數據
  localStorage.removeItem('saved_token')
  localStorage.removeItem('token_expiry')

  // 如果想保留用戶的郵箱，可以選擇不刪除，方便下次登入
  // localStorage.removeItem('saved_mail')
  // localStorage.removeItem('saved_password')

  // 更新 pinia 狀態
  isUser.value = false
  clientData.value = {}

  // 顯示登出成功訊息
  message.success('登出成功')

  // 重新載入頁面以更新所有組件狀態
  window.location.href = '/'
}

// 計算商品館子分類
const productSubCategories = computed(() => {
  const productCat = classSet.value.find((cat) => cat.label === '商品館')
  return productCat ? productCat.children || [] : []
})

// 計算品牌館子分類
const brandSubCategories = computed(() => {
  const brandCat = classSet.value.find((cat) => cat.label === '品牌館')
  return brandCat ? brandCat.children || [] : []
})

// 檢查分類是否有第三層或更深層的子分類
const hasDeepChildCategories = (category) => {
  if (!category.children || category.children.length === 0) {
    return false
  }
  
  for (const child of category.children) {
    if (child.children && child.children.length > 0) {
      return true
    }
  }
  
  return false
}

// 獲取分類層級
const getCategoryLevel = (category, level = 0) => {
  return {
    ...category,
    level: level,
    children: category.children?.map(child => getCategoryLevel(child, level + 1)) || []
  }
}

// 已在模板中直接使用固定的 grid-cols-5
</script>
<style scoped>
.auto-fit-text {
  font-size: clamp(0.75em, 0.9vw, 0.95em); /* 調整字體大小範圍 */
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  max-width: 100%;
  display: inline-block;
  line-height: 1.4;
  padding: 2px 0;
}

header {
  display: block !important;
  visibility: visible !important;
  opacity: 1 !important;
  height: 80px; /* 固定header高度 */
  position: fixed !important;
  top: 0 !important;
  left: 0 !important;
  right: 0 !important;
  width: 100% !important;
  z-index: 1000 !important; /* 增加z-index以確保顯示在最頂層 */
  background-color: white !important;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1) !important;
}

.container {
  width: 100%;
  max-width: 1280px;
  margin: 0 auto;
  padding: 0 1rem;
}

/* 改善下拉菜單樣式 */
.group:hover .group-hover\:block {
  display: block !important;
}

/* 添加過渡效果 */
.dropdown-menu {
  position: absolute;
  background-color: white;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  padding: 8px;
  z-index: 100; /* 提高z-index確保在所有頁面都顯示於頂層 */
  top: 100%;
  left: 50%;
  transform: translateX(-50%);
  width: max-content;
  min-width: 600px;
  max-width: 900px;
  border-radius: 4px;
  margin-top: 8px;
  max-height: 80vh; /* 限制最大高度 */
  overflow-y: auto; /* 允許垂直滾動 */
  
  /* 添加過渡效果 */
  opacity: 0;
  visibility: hidden;
  transition: opacity 0.3s, visibility 0.3s;
  
  /* 添加上方懸停區域，解決鼠標移動到菜單時的間隙問題 */
  padding-top: 20px;
  margin-top: -12px;
}

/* 當父元素或自身被懸停時顯示 */
.group:hover .dropdown-menu,
.dropdown-menu:hover {
  opacity: 1;
  visibility: visible;
}

/* 加寬集合的懸停區域 */
.relative.group {
  padding-bottom: 20px;
  margin-bottom: -20px;
}

/* 確保下拉選單在鼠標懸停於選單上時也保持顯示 */
.dropdown-menu.group-hover\:block:hover {
  display: block !important;
}

/* 品牌名稱樣式優化 */
a[href^="/brands"] {
  word-break: keep-all;
  overflow-wrap: break-word;
  hyphens: auto;
}

/* 品牌標題特定樣式 */
.brand-title {
  font-size: 0.95em;
  line-height: 1.3;
  padding: 3px 0;
  max-width: 100%;
  display: block;
}

/* 確保下拉菜單中的文字正確顯示 */
.grid-cols-3 a, .grid-cols-5 a {
  display: inline-block;
  max-width: 100%;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* 多層級分類樣式 */
.category-nested {
  margin-left: 0.75rem;
  border-left: 2px solid #e5e7eb;
  padding-left: 0.5rem;
  margin-top: 0.25rem;
  margin-bottom: 0.25rem;
}

.subcategory {
  font-size: 0.875rem;
  color: #4b5563;
}

.grandchild-category {
  font-size: 0.75rem;
  color: #6b7280;
}

/* 下拉選單容器 - 移除這個區塊因為已經被新的樣式替代 */
/* .dropdown-menu {
  position: absolute;
  background-color: white;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  padding: 8px;
  z-index: 100;
  top: 100%;
  left: 50%;
  transform: translateX(-50%);
  width: max-content;
  min-width: 600px;
  max-width: 900px;
  border-radius: 4px;
  margin-top: 8px;
  max-height: 80vh;
  overflow-y: auto;
} */

/* 修復下拉選單錯亂問題 */
.dropdown-menu .grid {
  display: grid;
  gap: 1rem;
  padding: 1rem;
}

/* 多層級分類樣式優化 */
.dropdown-menu .space-y-1 > * + * {
  margin-top: 0.25rem;
}

/* 第二層分類樣式 */
.dropdown-menu .pl-2 {
  padding-left: 0.5rem;
  border-left: 2px solid #f3f4f6;
}

/* 第三層分類樣式 */
.dropdown-menu .ml-4 {
  margin-left: 1rem;
}

/* 第四層分類樣式 */
.dropdown-menu .text-gray-400 {
  color: #9ca3af;
  font-size: 0.75rem;
}

/* 層級縮進效果 */
.dropdown-menu .border-l-2 {
  border-left-width: 2px;
  padding-left: 0.5rem;
}

.dropdown-menu .border-gray-200 {
  border-color: #e5e7eb;
}

.dropdown-menu .border-gray-100 {
  border-color: #f3f4f6;
}

/* 確保頁面header始終固定在頂部 */
header {
  height: 80px; /* 固定header高度 */
}

/* 確保container在所有裝置上正確顯示 */
@media (max-width: 1280px) {
  .dropdown-menu {
    min-width: 300px;
    max-width: 90vw;
  }
}
</style>
<template>
  <!-- :class="scrollY > 0 ? ' py-4' : 'py-10'" -->

  <header class="w-full fixed z-30 bg-white duration-300 py-5 shadow-md">
    <div class="container m-auto flex justify-between items-center">
      <RouterLink to="/">
        <img src="/src/assets/images/bannerlogo.png" alt="logo" class="w-2/3" />
      </RouterLink>

      <!-- <RouterLink to="/" class="z-20"><img src="@/assets/images/logo.webp" alt="logo" class="duration-700  w-1/2"
        :class="scrollY > 0 ? ' brightness-50' : 'scale-150 translate-x-8 translate-y-8'"></RouterLink> -->

      <div class="w-1/4 xl:flex justify-between fw-bold text-nowrap h-10 z-20 hidden">
        <ul class="flex space-x-4">
          <li>
            <RouterLink to="/" class="font-bold"> 首頁 </RouterLink>
          </li>
          <li>
            <RouterLink to="/aboutUs" class="font-bold"> 關於我們 </RouterLink>
          </li>
          <li class="relative group">
            <RouterLink :to="{ path: '/products', query: { sort: '商品館' } }" class="font-bold"> 商品館 </RouterLink>
            <!-- 商品館下拉菜單 -->
            <div
              class="dropdown-menu"
            >
              <!-- 使用 grid 佈局顯示商品館分類及其子分類 (固定5欄) -->
              <div
                v-if="productSubCategories.length > 0"
                class="grid grid-cols-5 gap-x-8 gap-y-4 p-4"
              >
                <!-- 遍歷商品館下的大分類 -->
                <div
                  v-for="topCategory in productSubCategories"
                  :key="topCategory.value"
                  class="space-y-1"
                >
                  <!-- 顯示大分類標題 (可點擊) -->
                  <RouterLink
                    :to="{
                      path: `/products`,
                      query: { sort: topCategory.value },
                    }"
                    class="block font-bold hover:text-blue-600 py-1 brand-title"
                    :title="topCategory.label"
                  >
                    {{ topCategory.label }}
                  </RouterLink>
                  <!-- 遞歸顯示所有子分類層級 -->
                  <div
                    v-if="topCategory.children && topCategory.children.length > 0"
                    class="flex flex-col space-y-1"
                  >
                    <!-- 使用遞歸組件顯示子分類 -->
                    <template v-for="subCategory in topCategory.children" :key="subCategory.value">
                      <!-- 第二層分類 -->
                      <RouterLink
                        :to="{
                          path: `/products`,
                          query: { sort: subCategory.value },
                        }"
                        class="block text-sm text-gray-600 hover:text-blue-600 py-0.5 auto-fit-text pl-2"
                        :title="subCategory.label"
                      >
                        {{ subCategory.label }}
                      </RouterLink>
                      
                      <!-- 第三層分類 -->
                      <div
                        v-if="subCategory.children && subCategory.children.length > 0"
                        class="ml-4 border-l-2 border-gray-200 pl-2 space-y-1"
                      >
                        <template v-for="thirdCategory in subCategory.children" :key="thirdCategory.value">
                          <RouterLink
                            :to="{
                              path: `/products`,
                              query: { sort: thirdCategory.value },
                            }"
                            class="block text-xs text-gray-500 hover:text-blue-600 py-0.5 auto-fit-text"
                            :title="thirdCategory.label"
                          >
                            {{ thirdCategory.label }}
                          </RouterLink>
                          
                          <!-- 第四層分類 -->
                          <div
                            v-if="thirdCategory.children && thirdCategory.children.length > 0"
                            class="ml-4 border-l-2 border-gray-100 pl-2 space-y-1"
                          >
                            <RouterLink
                              v-for="fourthCategory in thirdCategory.children"
                              :key="fourthCategory.value"
                              :to="{
                                path: `/products`,
                                query: { sort: fourthCategory.value },
                              }"
                              class="block text-xs text-gray-400 hover:text-blue-600 py-0.5 auto-fit-text"
                              :title="fourthCategory.label"
                            >
                              {{ fourthCategory.label }}
                            </RouterLink>
                          </div>
                        </template>
                      </div>
                    </template>
                  </div>
                </div>
              </div>
              <div v-else class="p-4 text-center text-gray-500">沒有商品分類</div>
            </div>
          </li>
          <li class="relative group">
            <RouterLink to="/brands" class="font-bold"> 品牌館 </RouterLink>
            <!-- 品牌館下拉菜單 (與商品館一致的呈現方式) -->
            <div
              class="dropdown-menu"
            >
              <!-- 使用 grid 佈局顯示品牌館分類及其子分類 (固定3欄) -->
              <div
                v-if="brandSubCategories.length > 0"
                class="grid grid-cols-3 gap-x-8 gap-y-4 p-4"
              >
                <!-- 遍歷品牌館下的大分類 -->
                <div
                  v-for="topCategory in brandSubCategories"
                  :key="topCategory.value"
                  class="space-y-1"
                >
                  <!-- 顯示大分類標題 (可點擊) -->
                  <RouterLink
                    :to="{
                      path: `/brands`,
                      query: { sort: topCategory.value },
                    }"
                    class="block font-bold hover:text-blue-600 py-1 brand-title"
                    :title="topCategory.label"
                  >
                    {{ topCategory.label }}
                  </RouterLink>
                  <!-- 遞歸顯示所有子分類層級 -->
                  <div
                    v-if="topCategory.children && topCategory.children.length > 0"
                    class="flex flex-col space-y-1"
                  >
                    <!-- 使用遞歸組件顯示子分類 -->
                    <template v-for="subCategory in topCategory.children" :key="subCategory.value">
                      <!-- 第二層分類 -->
                      <RouterLink
                        :to="{
                          path: `/brands`,
                          query: { sort: subCategory.value },
                        }"
                        class="block text-sm text-gray-600 hover:text-blue-600 py-0.5 auto-fit-text pl-2"
                        :title="subCategory.label"
                      >
                        {{ subCategory.label }}
                      </RouterLink>
                      
                      <!-- 第三層分類 -->
                      <div
                        v-if="subCategory.children && subCategory.children.length > 0"
                        class="ml-4 border-l-2 border-gray-200 pl-2 space-y-1"
                      >
                        <template v-for="thirdCategory in subCategory.children" :key="thirdCategory.value">
                          <RouterLink
                            :to="{
                              path: `/brands`,
                              query: { sort: thirdCategory.value },
                            }"
                            class="block text-xs text-gray-500 hover:text-blue-600 py-0.5 auto-fit-text"
                            :title="thirdCategory.label"
                          >
                            {{ thirdCategory.label }}
                          </RouterLink>
                          
                          <!-- 第四層分類 -->
                          <div
                            v-if="thirdCategory.children && thirdCategory.children.length > 0"
                            class="ml-4 border-l-2 border-gray-100 pl-2 space-y-1"
                          >
                            <RouterLink
                              v-for="fourthCategory in thirdCategory.children"
                              :key="fourthCategory.value"
                              :to="{
                                path: `/brands`,
                                query: { sort: fourthCategory.value },
                              }"
                              class="block text-xs text-gray-400 hover:text-blue-600 py-0.5 auto-fit-text"
                              :title="fourthCategory.label"
                            >
                              {{ fourthCategory.label }}
                            </RouterLink>
                          </div>
                        </template>
                      </div>
                    </template>
                  </div>
                </div>
              </div>

              <div
                v-if="brandSubCategories.length === 0 && brands.length === 0"
                class="p-4 text-center text-gray-500"
              >
                沒有品牌分類
              </div>
            </div>
          </li>
          <li>
            <RouterLink to="/member" class="font-bold"> 社員專區 </RouterLink>
          </li>

          <!-- 移除舊的分類顯示邏輯 -->
        </ul>
      </div>

      <!-- <div class=" w-1/4  lg:flex justify-between fw-bold text-nowrap h-10 z-20 hidden relative">

        <RouterLink to="/關於我們">
          <div class="font-bold ">
            關於我們
          </div>
        </RouterLink>

        <div v-for="i in setClass" :key="i.value" class="  ">

          <RouterLink to="/product" class="font-bold " @mouseenter="() => (children = i.children)(showChildren = true)"
            @mouseleave="() => (showChildren = false)">
            {{ i.label }}
          </RouterLink>


        </div>

        <div class="bg-sky-500 absolute left-0 w-[900px] p-5  duration-300 z-10 top-14" v-if="showChildren"
          @mouseenter="() => (showChildren = true)" @mouseleave="() => (showChildren = false)">

          <div class="grid grid-cols-4 gap-20">

            <RouterLink to="/product" v-for="i in children" :key="i.value">
              <div class="font-bold">
                {{ i.label }}

                <div class="font-normal flex flex-col">
                  <RouterLink to="/product" v-for="e in i.children" :key="e.value">
                    {{ e.label }}2
                  </RouterLink>

                </div>
              </div>

            </RouterLink>

          </div>

        </div>


      </div> -->

      <!-- <div class="lg:w-1/3 w-1/2 group relative">

        <div class=" overflow-hidden  lg:flex justify-between fw-bold text-nowrap h-10 z-20 hidden ">

          <RouterLink to="/關於我們" >
            <div class="font-bold ">
              關於我們
            </div>
            <div>
              About Us
            </div>
          </RouterLink>
          <RouterLink to="/product" class="   hover:-translate-y-9  duration-300 ">
            <div class="font-bold ">
              服務項目
            </div>
            <div>
              Service
            </div>

          </RouterLink>
          <RouterLink to="/news" class="   hover:-translate-y-9  duration-300 ">
            <div class="font-bold ">
              最新消息
            </div>
            <div>
              News
            </div>
          </RouterLink>
          <RouterLink to="/contact" class="   hover:-translate-y-9  duration-300 ">
            <div class="font-bold ">
              聯絡我們
            </div>
            <div>
              Contact Us
            </div>
          </RouterLink>

        </div>

        <div class="w-full bg-"></div>
      </div> -->

      <div class="flex items-center">
        <div class="lg:block hidden">
          <div class="flex justify-between items-center mr-4">
            <input
              v-model="searchName"
              type="text"
              class="border-b-2"
              @keyup.enter="searchProducts"
            />
            <button type="button" @click="searchProducts">
              <img src="/src/assets/images/icon/search.svg" class="w-8" alt="search" />
            </button>
          </div>
        </div>

        <!-- 用戶區域 -->
        <div class="flex items-center">
          <template v-if="isUser">
            <div class="mr-2 text-sm hidden md:block">您好，{{ clientData?.name || '會員' }}</div>

            <div class="flex items-center">
              <!-- 使用下拉菜單包裹頭像，頭像本身作為觸發元素 -->
              <a-dropdown>
                <div class="cursor-pointer">
                  <!-- 頭像作為下拉菜單的觸發元素 -->
                  <img src="/src/assets/images/icon/person.svg" alt="menu" class="w-8 mr-4" />
                </div>
                <template #overlay>
                  <a-menu>
                    <a-menu-item key="account">
                      <router-link to="/account">個人中心</router-link>
                    </a-menu-item>
                    <a-menu-item key="logout" @click="logout"> 登出 </a-menu-item>
                  </a-menu>
                </template>
              </a-dropdown>
            </div>
          </template>
          <template v-else>
            <RouterLink to="/login">
              <img src="/src/assets/images/icon/person.svg" alt="menu" class="w-8 mr-4" />
            </RouterLink>
          </template>
        </div>

        <!-- <a href=""> <img src="@/assets/images/icon/shopping_cart.svg" alt="menu" class="w-8"> </a> -->
        <button type="primary" @click="() => (cartsOpen = !cartsOpen)" class="relative">
          <img src="/src/assets/images/icon/shopping_cart.svg" alt="menu" class="w-8" />
          <div
            v-if="carts.length > 0"
            class="absolute -top-2 -right-2 bg-red-600 rounded-full w-6 h-6 text-sm text-white flex justify-center items-center"
          >
            {{ carts.length }}
          </div>
        </button>

        <button type="button " @click="openMenu" class="z-10 xl:hidden block ml-4">
          <img src="/src/assets/images/icon/menu.svg" class="w-8" alt="menu" />
        </button>
      </div>

      <!-- <RouterLink to="/" class="z-20"><img src="@/assets/images/logo.webp" alt="logo" class="duration-700  w-1/2">
      </RouterLink> -->

      <!-- <div class="bg-[#d9c8a5] w-full absolute top-0 left-0   duration-700 "
      :class="scrollY > 0 ? 'p-11' : 'bg-opacity-90'">
    </div> -->

      <!-- <button type="button " @click="openMenu" class="z-10 md:hidden w-10">

        <img src="@/assets/images/icon/menu.svg" alt="menu" class="duration-700 "
          :class="scrollY > 0 ? ' brightness-50 ' : ''">

      </button> -->
      <!-- :class="cartsOpen ? 'translate-x-0' : ' translate-x-full'" -->
    </div>
  </header>
  <!-- :class="cartsOpen ? 'translate-y-20' : '-translate-y-full'" -->
  <!-- <a-drawer v-model:open="cartsOpen" height="500" class="custom-class" root-class-name="root-class-name" title="購物車"
    placement="left">
    <div class="container m-auto">
      <CartItem v-if="carts.length > 0"></CartItem>
      <p class="text-center" v-else>購物車是空的</p>
      <div class="flex justify-end" v-if="carts.length > 0">

        <button @click="goPay" class="bg-black text-white w-1/6 p-2 mt-4">前往結帳</button>

      </div>

    </div>
  </a-drawer> -->
  <!-- 手機選單 -->

  <div
    class="z-20 bg-white shadow-md h-screen fixed top-0 right-0 w-full px-4 pt-20 duration-700 xl:hidden flex flex-col items-center translate-y-16 overflow-auto"
    :class="menuOpen ? 'translate-x-0' : ' translate-x-full'"
  >
    <!-- 全部商品移到最上面 -->
    <RouterLink
      class="text-xl w-full py-2"
      :to="{
        path: `/products`,
        query: { sort: '全部商品' }
      }"
      @click="() => (menuOpen = false)"
    >
      全部商品
    </RouterLink>

    <RouterLink to="/products" class="text-xl w-full py-2" @click="() => (menuOpen = false)">
      商品館
    </RouterLink>
    <RouterLink to="/brands" class="text-xl w-full py-2" @click="() => (menuOpen = false)">
      品牌館
    </RouterLink>
    <RouterLink to="/member" class="text-xl w-full py-2" @click="() => (menuOpen = false)">
      社員專區
    </RouterLink>

    <!-- 新增個人中心和登出按鈕 -->
    <div v-if="isUser" class="w-full">
      <RouterLink to="/account" class="text-xl w-full py-2 block font-bold" @click="() => (menuOpen = false)">
        個人中心
      </RouterLink>
      <button
        @click="
          () => {
            logout()
            menuOpen = false
          }
        "
        class="text-xl w-full py-2 text-left"
      >
        登出
      </button>
    </div>
    <RouterLink v-else to="/login" class="text-xl w-full py-2" @click="() => (menuOpen = false)">
      登入/註冊
    </RouterLink>

    <!-- 隱藏商品分類下拉選單 - 註解掉樹組件 -->
    <!-- <a-tree
      class="text-xl w-full"
      :tree-data="classSet.filter((cat) => cat.label !== '品牌館')"
      :expandedKeys="expandedKeys"
      @expand="onExpand"
    >
      <template #title="{ value, label }">
        <RouterLink
          :to="{
            path: `/products`,
            query: { sort: value },
          }"
          style="line-height: 2"
          @click="() => (menuOpen = false)"
          >{{ label }}</RouterLink
        >
      </template>
    </a-tree> -->
    <!-- <RouterLink to="/AboutUs">

      關於我們

    </RouterLink>
    <RouterLink to="/service">

      服務項目


    </RouterLink>
    <RouterLink to="/news">

      最新消息


    </RouterLink>
    <RouterLink to="/AboutUs">

      聯絡我們


    </RouterLink> -->
  </div>

  <div
    v-if="showChildren && menuList.length > 0"
    class="bg-white shadow-md fixed left-1/2 -translate-x-1/2 top-20 w-1/2 z-20"
    @mouseleave="() => (showChildren = false)"
  >
    <ul class="flex flex-wrap gap-10 p-5">
      <li v-for="item in menuList" :key="item.value">
        <RouterLink
          :to="{
            path: `/products`,
            query: { sort: item.value },
          }"
        >
          <div class="font-bold">
            {{ item.label }}
            <div class="font-normal flex flex-col">
              <RouterLink
                :to="{
                  path: `/products`,
                  query: { sort: e.value },
                }"
                v-for="e in item.children"
                :key="e.value"
              >
                {{ e.label }}
              </RouterLink>
            </div>
          </div>
        </RouterLink>
      </li>
    </ul>
  </div>

  <div
    class="bg-gray-700 h-screen w-screen fixed z-30 opacity-45"
    :class="cartsOpen ? '' : 'hidden'"
    @click="() => (cartsOpen = false)"
  ></div>
  <div
    class="bg-white h-1/2 z-30 fixed w-full bottom-0 left-0 duration-300 lg:p-20 p-4 shadow-xl overflow-auto"
    :class="cartsOpen ? '' : 'translate-y-full'"
    ref="cartDiv"
  >
    <div class="container m-auto">
      <CartItem v-if="carts.length > 0"></CartItem>
      <p class="text-center" v-else>購物車是空的</p>
      <div class="flex justify-end" v-if="carts.length > 0">
        <button @click="goPay" class="bg-black text-white md:w-1/6 w-full p-2 mt-4">
          前往結帳
        </button>
      </div>
    </div>
  </div>

  <!-- <div class="bg-[#d9c8a5] w-full absolute top-0 left-0   duration-700 "
    :class="scrollY > 0 ? 'p-11' : 'bg-opacity-90'">
  </div> -->
</template>
