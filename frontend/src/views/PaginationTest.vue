<template>
  <div class="pagination-test-page">
    <div class="container mx-auto px-4 py-8">
      <h1 class="text-3xl font-bold text-center mb-8">分頁組件測試頁面</h1>
      
      <!-- 測試說明 -->
      <div class="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-8">
        <h2 class="text-lg font-semibold text-blue-800 mb-2">測試項目：</h2>
        <ul class="text-blue-700 space-y-1">
          <li>• 手機版響應式分頁顯示</li>
          <li>• 分頁數字不會擠在一起變多排</li>
          <li>• 支援水平滾動查看更多頁碼</li>
          <li>• 根據螢幕大小自動調整顯示的頁碼數量</li>
        </ul>
      </div>

      <!-- 測試控制 -->
      <div class="bg-white rounded-lg shadow p-6 mb-8">
        <h3 class="text-lg font-semibold mb-4">測試控制</h3>
        <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">總項目數：</label>
            <input 
              v-model.number="totalItems" 
              type="number" 
              min="1" 
              max="10000"
              class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">每頁顯示：</label>
            <select 
              v-model.number="pageSize"
              class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option :value="10">10 項</option>
              <option :value="20">20 項</option>
              <option :value="50">50 項</option>
              <option :value="100">100 項</option>
            </select>
          </div>
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">當前頁：</label>
            <input 
              v-model.number="currentPage" 
              type="number" 
              :min="1" 
              :max="Math.ceil(totalItems / pageSize)"
              class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>
        </div>
      </div>

      <!-- 分頁組件測試 -->
      <div class="bg-white rounded-lg shadow p-6">
        <h3 class="text-lg font-semibold mb-4">分頁組件</h3>
        
        <!-- 模擬數據列表 -->
        <div class="mb-6">
          <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            <div 
              v-for="item in currentPageItems" 
              :key="item.id"
              class="bg-gray-50 border border-gray-200 rounded-lg p-4"
            >
              <h4 class="font-medium text-gray-900">項目 #{{ item.id }}</h4>
              <p class="text-sm text-gray-600 mt-1">這是第 {{ item.id }} 個測試項目</p>
            </div>
          </div>
        </div>

        <!-- 分頁組件 -->
        <PageItem
          :total-page="totalItems"
          :initial-page="currentPage"
          :page-size="pageSize"
          :loading="loading"
          @page-change="handlePageChange"
          @page-size-change="handlePageSizeChange"
        />
      </div>

      <!-- 響應式測試提示 -->
      <div class="mt-8 bg-yellow-50 border border-yellow-200 rounded-lg p-4">
        <h3 class="text-lg font-semibold text-yellow-800 mb-2">響應式測試提示：</h3>
        <div class="text-yellow-700 space-y-2">
          <p>• 請調整瀏覽器視窗大小或使用開發者工具的裝置模擬器測試</p>
          <p>• 在手機版 (≤640px) 時，分頁數字會減少到5個，並支援水平滾動</p>
          <p>• 在極小螢幕 (≤480px) 時，按鈕會進一步縮小，間距會更緊湊</p>
          <p>• 分頁按鈕不會換行變成多排，而是保持在同一行並可滾動</p>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue'
import PageItem from '@/components/utils/PageItem.vue'

// 測試數據
const totalItems = ref(1000)
const pageSize = ref(20)
const currentPage = ref(1)
const loading = ref(false)

// 計算當前頁的項目
const currentPageItems = computed(() => {
  const start = (currentPage.value - 1) * pageSize.value + 1
  const end = Math.min(currentPage.value * pageSize.value, totalItems.value)
  
  return Array.from({ length: end - start + 1 }, (_, index) => ({
    id: start + index,
    name: `項目 ${start + index}`
  }))
})

// 處理頁面變更
const handlePageChange = (page) => {
  loading.value = true
  currentPage.value = page
  
  // 模擬載入延遲
  setTimeout(() => {
    loading.value = false
  }, 500)
}

// 處理每頁大小變更
const handlePageSizeChange = (newPageSize) => {
  loading.value = true
  pageSize.value = newPageSize
  currentPage.value = 1
  
  // 模擬載入延遲
  setTimeout(() => {
    loading.value = false
  }, 500)
}
</script>

<style scoped>
.pagination-test-page {
  min-height: 100vh;
  background-color: #f8fafc;
}
</style>
