import { createRouter, createWebHistory } from 'vue-router'
import { message } from 'ant-design-vue'

// Token 驗證函數
const validateToken = () => {
  const token = localStorage.getItem('token')
  const isLogin = localStorage.getItem('isLogin')
  
  // 如果沒有 token 或 isLogin 標記，則未登入
  if (!token || !isLogin || token === 'null' || token === 'undefined') {
    return false
  }
  
  try {
    // 簡單的 token 格式驗證 (JWT 應該有三部分)
    const parts = token.split('.')
    if (parts.length !== 3) {
      console.warn('🔐 Token 格式無效')
      return false
    }
    
    // 解析 token payload 檢查過期時間
    const payload = JSON.parse(atob(parts[1]))
    if (payload.exp) {
      const currentTime = Math.floor(Date.now() / 1000)
      if (payload.exp < currentTime) {
        console.warn('🔐 Token 已過期')
        return false
      }
    }
    
    return true
  } catch (error) {
    console.error('🔐 Token 驗證失敗:', error)
    return false
  }
}

// 清除無效的登入狀態
const clearInvalidAuth = () => {
  const authKeys = ['token', 'isLogin', 'user', 'userData', 'belongCp']
  authKeys.forEach(key => {
    localStorage.removeItem(key)
  })
  console.log('✅ 已清除無效的認證資料')
}

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  // 優化滾動行為設定，改善用戶體驗
  scrollBehavior(to, from, savedPosition) {
    // 特殊情況：跳轉到商品詳情頁面時，總是滾動到頂部
    if (to.path === '/product') {
      return { top: 0, behavior: 'instant' }
    }
    
    // 特殊情況：從商品詳情頁面返回商品列表時，嘗試恢復位置
    if (from.path === '/product' && to.path === '/products' && savedPosition) {
      return { ...savedPosition, behavior: 'instant' }
    }
    
    // 其他需要立即滾動到頂部的頁面
    const immediateScrollPages = ['/check', '/login', '/account']
    if (immediateScrollPages.includes(to.path)) {
      return { top: 0, behavior: 'instant' }
    }
    
    // 一般情況：如果有保存的位置且是瀏覽器前進/後退，才恢復位置
    if (savedPosition && from.path !== to.path) {
      // 添加延遲確保頁面內容已載入
      return new Promise((resolve) => {
        setTimeout(() => {
          resolve({ ...savedPosition, behavior: 'smooth' })
        }, 100)
      })
    }
    
    // 默認滾動到頂部
    return { top: 0, behavior: 'smooth' }
  },
  routes: [
    {
      path: '/',
      name: 'home',
      component: () => import('../views/HomeView.vue'),
    },
    {
      path: '/aboutUs',
      component: () => import('../views/inform/AboutView.vue'),
    },
    {
      path: '/shoppingNotice',
      component: () => import('../views/inform/ShoppingNotice.vue'),
    },
    {
      path: '/transportNotice',
      component: () => import('../views/inform/TransportNotice.vue'),
    },
    {
      path: '/privacy',
      name: 'privacy',
      component: () => import('../views/inform/PrivacyView.vue'),
    },
    {
      path: '/login',
      component: () => import('../views/LoginView.vue'),
    },
    {
      path: '/account',
      component: () => import('../views/AccountView.vue'),
      meta: { isLogin: true },
    },
    {
      path: '/news',
      name: 'news',
      component: () => import('../views/NewsView.vue'),
    },
    {
      path: '/service',
      name: 'service',
      component: () => import('../views/ServiceView.vue'),
    },
    {
      path: '/products',
      component: () => import('../views/ProductsView.vue'),
    },
    {
      path: '/product',
      component: () => import('../views/ProductInfo.vue'),
    },
    {
      path: '/check',
      component: () => import('../views/CheckView.vue'),
      meta: { isLogin: true },
    },
    {
      path: '/brands',
      component: () => import('../views/BrandsView.vue'),
    },
    {
      path: '/brands/all',
      name: 'allBrands',
      component: () => import('../views/BrandDetailView.vue'),
    },
    {
      path: '/brand/:id',
      component: () => import('../views/BrandDetailView.vue'),
    },
    {
      path: '/pagination-test',
      name: 'paginationTest',
      component: () => import('../views/PaginationTest.vue'),
    },
    {
      path: '/member',
      redirect: '/account',
      meta: { isLogin: true },
    },
    {
      path: '/admin',
      redirect: 'http://localhost:5173/',
    },
    {
      path: '/:pathMatch(.*)*',
      redirect: '/',
    },
  ],
})

router.beforeEach((to, from, next) => {
  // 前台登入檢查 - 增強版本
  if (to.meta.isLogin) {
    const isValidLogin = validateToken()
    
    if (!isValidLogin) {
      console.warn('🔐 前台路由守衛：Token無效或已過期，重導向到登入頁')
      clearInvalidAuth()
      
      // 如果不是已經在登入頁面，顯示提示訊息
      if (from.path !== '/login') {
        message.warning('登入已過期，請重新登入')
      }
      
      return next('/login')
    }
  }

  // 社員專區檢查 - 增強版本
  if (to.path === '/products' && to.query.sort === '社員專區') {
    const isValidLogin = validateToken()
    
    if (!isValidLogin) {
      console.warn('🔐 前台路由守衛：訪問社員專區但Token無效，重導向到登入頁')
      clearInvalidAuth()
      
      if (from.path !== '/login') {
        message.warning('請先登入以訪問社員專區')
      }
      
      return next('/login')
    }
  }

  next()
})

export default router
